{"runtimeTarget": {"name": ".NETCoreApp,Version=v8.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v8.0": {"CorzaVeevaIntegration.CommonFunctions/1.0.0": {"dependencies": {"Azure.Messaging.ServiceBus": "7.19.0", "Microsoft.Azure.Functions.Worker.Extensions.ServiceBus": "5.22.2", "Microsoft.Extensions.Http": "9.0.4", "Microsoft.Extensions.Logging.Abstractions": "9.0.4", "Newtonsoft.Json": "13.0.3"}, "runtime": {"CorzaVeevaIntegration.CommonFunctions.dll": {}}}, "Azure.Core/1.44.1": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "6.0.0", "System.ClientModel": "1.1.0", "System.Diagnostics.DiagnosticSource": "9.0.4", "System.Memory.Data": "6.0.0", "System.Numerics.Vectors": "4.5.0", "System.Text.Encodings.Web": "6.0.0", "System.Text.Json": "6.0.10", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/net6.0/Azure.Core.dll": {"assemblyVersion": "1.44.1.0", "fileVersion": "1.4400.124.50905"}}}, "Azure.Core.Amqp/1.3.1": {"dependencies": {"Microsoft.Azure.Amqp": "2.6.9", "System.Memory": "4.5.4", "System.Memory.Data": "6.0.0"}, "runtime": {"lib/netstandard2.0/Azure.Core.Amqp.dll": {"assemblyVersion": "1.3.1.0", "fileVersion": "1.300.124.38003"}}}, "Azure.Identity/1.12.0": {"dependencies": {"Azure.Core": "1.44.1", "Microsoft.Identity.Client": "4.61.3", "Microsoft.Identity.Client.Extensions.Msal": "4.61.3", "System.Memory": "4.5.4", "System.Security.Cryptography.ProtectedData": "4.7.0", "System.Text.Json": "6.0.10", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/netstandard2.0/Azure.Identity.dll": {"assemblyVersion": "1.12.0.0", "fileVersion": "1.1200.24.31701"}}}, "Azure.Messaging.ServiceBus/7.19.0": {"dependencies": {"Azure.Core": "1.44.1", "Azure.Core.Amqp": "1.3.1", "Microsoft.Azure.Amqp": "2.6.9"}, "runtime": {"lib/net8.0/Azure.Messaging.ServiceBus.dll": {"assemblyVersion": "7.19.0.0", "fileVersion": "7.1900.25.20802"}}}, "Google.Protobuf/3.27.1": {"runtime": {"lib/net5.0/Google.Protobuf.dll": {"assemblyVersion": "3.27.1.0", "fileVersion": "3.27.1.0"}}}, "Grpc.Core.Api/2.55.0": {"dependencies": {"System.Memory": "4.5.4"}, "runtime": {"lib/netstandard2.1/Grpc.Core.Api.dll": {"assemblyVersion": "*******", "fileVersion": "********"}}}, "Grpc.Net.Client/2.55.0": {"dependencies": {"Grpc.Net.Common": "2.55.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.4"}, "runtime": {"lib/net7.0/Grpc.Net.Client.dll": {"assemblyVersion": "*******", "fileVersion": "********"}}}, "Grpc.Net.ClientFactory/2.55.0": {"dependencies": {"Grpc.Net.Client": "2.55.0", "Microsoft.Extensions.Http": "9.0.4"}, "runtime": {"lib/net7.0/Grpc.Net.ClientFactory.dll": {"assemblyVersion": "*******", "fileVersion": "********"}}}, "Grpc.Net.Common/2.55.0": {"dependencies": {"Grpc.Core.Api": "2.55.0"}, "runtime": {"lib/net7.0/Grpc.Net.Common.dll": {"assemblyVersion": "*******", "fileVersion": "********"}}}, "Microsoft.Azure.Amqp/2.6.9": {"runtime": {"lib/netstandard2.0/Microsoft.Azure.Amqp.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.Azure.Functions.Worker.Core/1.19.0": {"dependencies": {"Azure.Core": "1.44.1", "Microsoft.Extensions.Hosting": "5.0.0", "Microsoft.Extensions.Hosting.Abstractions": "5.0.0", "System.Collections.Immutable": "5.0.0", "System.Diagnostics.DiagnosticSource": "9.0.4"}, "runtime": {"lib/net5.0/Microsoft.Azure.Functions.Worker.Core.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.Azure.Functions.Worker.Extensions.Abstractions/1.3.0": {"runtime": {"lib/netstandard2.0/Microsoft.Azure.Functions.Worker.Extensions.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.Azure.Functions.Worker.Extensions.Rpc/1.0.1": {"dependencies": {"Grpc.Net.Client": "2.55.0", "Grpc.Net.ClientFactory": "2.55.0", "Microsoft.Azure.Functions.Worker.Core": "1.19.0", "Microsoft.Azure.Functions.Worker.Extensions.Abstractions": "1.3.0"}, "runtime": {"lib/net6.0/Microsoft.Azure.Functions.Worker.Extensions.Rpc.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.Azure.Functions.Worker.Extensions.ServiceBus/5.22.2": {"dependencies": {"Azure.Identity": "1.12.0", "Azure.Messaging.ServiceBus": "7.19.0", "Google.Protobuf": "3.27.1", "Microsoft.Azure.Functions.Worker.Extensions.Abstractions": "1.3.0", "Microsoft.Azure.Functions.Worker.Extensions.Rpc": "1.0.1", "Microsoft.Extensions.Azure": "1.7.5"}, "runtime": {"lib/netstandard2.0/Microsoft.Azure.Functions.Worker.Extensions.ServiceBus.dll": {"assemblyVersion": "5.22.2.0", "fileVersion": "5.22.2.0"}}}, "Microsoft.Bcl.AsyncInterfaces/6.0.0": {"runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "Microsoft.Extensions.Azure/1.7.5": {"dependencies": {"Azure.Core": "1.44.1", "Azure.Identity": "1.12.0", "Microsoft.Extensions.Configuration.Abstractions": "9.0.4", "Microsoft.Extensions.Configuration.Binder": "9.0.4", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.4", "Microsoft.Extensions.Logging.Abstractions": "9.0.4", "Microsoft.Extensions.Options": "9.0.4"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Azure.dll": {"assemblyVersion": "1.7.5.0", "fileVersion": "1.700.524.41504"}}}, "Microsoft.Extensions.Configuration/9.0.4": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.4", "Microsoft.Extensions.Primitives": "9.0.4"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}}, "Microsoft.Extensions.Configuration.Abstractions/9.0.4": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.4"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}}, "Microsoft.Extensions.Configuration.Binder/9.0.4": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.4"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Binder.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}}, "Microsoft.Extensions.Configuration.CommandLine/5.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.4", "Microsoft.Extensions.Configuration.Abstractions": "9.0.4"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.CommandLine.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "Microsoft.Extensions.Configuration.EnvironmentVariables/5.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.4", "Microsoft.Extensions.Configuration.Abstractions": "9.0.4"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "Microsoft.Extensions.Configuration.FileExtensions/5.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.4", "Microsoft.Extensions.Configuration.Abstractions": "9.0.4", "Microsoft.Extensions.FileProviders.Abstractions": "5.0.0", "Microsoft.Extensions.FileProviders.Physical": "5.0.0", "Microsoft.Extensions.Primitives": "9.0.4"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "Microsoft.Extensions.Configuration.Json/5.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.4", "Microsoft.Extensions.Configuration.Abstractions": "9.0.4", "Microsoft.Extensions.Configuration.FileExtensions": "5.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "5.0.0"}, "runtime": {"lib/netstandard2.1/Microsoft.Extensions.Configuration.Json.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "Microsoft.Extensions.Configuration.UserSecrets/5.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.4", "Microsoft.Extensions.Configuration.Json": "5.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "5.0.0", "Microsoft.Extensions.FileProviders.Physical": "5.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.UserSecrets.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "Microsoft.Extensions.DependencyInjection/9.0.4": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.4"}, "runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.4": {"runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}}, "Microsoft.Extensions.Diagnostics/9.0.4": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.4", "Microsoft.Extensions.Diagnostics.Abstractions": "9.0.4", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.4"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Diagnostics.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}}, "Microsoft.Extensions.Diagnostics.Abstractions/9.0.4": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.4", "Microsoft.Extensions.Options": "9.0.4", "System.Diagnostics.DiagnosticSource": "9.0.4"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Diagnostics.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}}, "Microsoft.Extensions.FileProviders.Abstractions/5.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.4"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "Microsoft.Extensions.FileProviders.Physical/5.0.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "5.0.0", "Microsoft.Extensions.FileSystemGlobbing": "5.0.0", "Microsoft.Extensions.Primitives": "9.0.4"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.FileProviders.Physical.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "Microsoft.Extensions.FileSystemGlobbing/5.0.0": {"runtime": {"lib/netstandard2.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "Microsoft.Extensions.Hosting/5.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.4", "Microsoft.Extensions.Configuration.Abstractions": "9.0.4", "Microsoft.Extensions.Configuration.Binder": "9.0.4", "Microsoft.Extensions.Configuration.CommandLine": "5.0.0", "Microsoft.Extensions.Configuration.EnvironmentVariables": "5.0.0", "Microsoft.Extensions.Configuration.FileExtensions": "5.0.0", "Microsoft.Extensions.Configuration.Json": "5.0.0", "Microsoft.Extensions.Configuration.UserSecrets": "5.0.0", "Microsoft.Extensions.DependencyInjection": "9.0.4", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.4", "Microsoft.Extensions.FileProviders.Abstractions": "5.0.0", "Microsoft.Extensions.FileProviders.Physical": "5.0.0", "Microsoft.Extensions.Hosting.Abstractions": "5.0.0", "Microsoft.Extensions.Logging": "9.0.4", "Microsoft.Extensions.Logging.Abstractions": "9.0.4", "Microsoft.Extensions.Logging.Configuration": "5.0.0", "Microsoft.Extensions.Logging.Console": "5.0.0", "Microsoft.Extensions.Logging.Debug": "5.0.0", "Microsoft.Extensions.Logging.EventLog": "5.0.0", "Microsoft.Extensions.Logging.EventSource": "5.0.0", "Microsoft.Extensions.Options": "9.0.4"}, "runtime": {"lib/netstandard2.1/Microsoft.Extensions.Hosting.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "Microsoft.Extensions.Hosting.Abstractions/5.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.4", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.4", "Microsoft.Extensions.FileProviders.Abstractions": "5.0.0"}, "runtime": {"lib/netstandard2.1/Microsoft.Extensions.Hosting.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "Microsoft.Extensions.Http/9.0.4": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.4", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.4", "Microsoft.Extensions.Diagnostics": "9.0.4", "Microsoft.Extensions.Logging": "9.0.4", "Microsoft.Extensions.Logging.Abstractions": "9.0.4", "Microsoft.Extensions.Options": "9.0.4"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Http.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}}, "Microsoft.Extensions.Logging/9.0.4": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "9.0.4", "Microsoft.Extensions.Logging.Abstractions": "9.0.4", "Microsoft.Extensions.Options": "9.0.4"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}}, "Microsoft.Extensions.Logging.Abstractions/9.0.4": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.4", "System.Diagnostics.DiagnosticSource": "9.0.4"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}}, "Microsoft.Extensions.Logging.Configuration/5.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.4", "Microsoft.Extensions.Configuration.Abstractions": "9.0.4", "Microsoft.Extensions.Configuration.Binder": "9.0.4", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.4", "Microsoft.Extensions.Logging": "9.0.4", "Microsoft.Extensions.Logging.Abstractions": "9.0.4", "Microsoft.Extensions.Options": "9.0.4", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.4"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Logging.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "Microsoft.Extensions.Logging.Console/5.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.4", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.4", "Microsoft.Extensions.Logging": "9.0.4", "Microsoft.Extensions.Logging.Abstractions": "9.0.4", "Microsoft.Extensions.Logging.Configuration": "5.0.0", "Microsoft.Extensions.Options": "9.0.4", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.4"}, "runtime": {"lib/netcoreapp3.0/Microsoft.Extensions.Logging.Console.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "Microsoft.Extensions.Logging.Debug/5.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.4", "Microsoft.Extensions.Logging": "9.0.4", "Microsoft.Extensions.Logging.Abstractions": "9.0.4"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Logging.Debug.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "Microsoft.Extensions.Logging.EventLog/5.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.4", "Microsoft.Extensions.Logging": "9.0.4", "Microsoft.Extensions.Logging.Abstractions": "9.0.4", "Microsoft.Extensions.Options": "9.0.4", "System.Diagnostics.EventLog": "5.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Logging.EventLog.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "Microsoft.Extensions.Logging.EventSource/5.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.4", "Microsoft.Extensions.Logging": "9.0.4", "Microsoft.Extensions.Logging.Abstractions": "9.0.4", "Microsoft.Extensions.Options": "9.0.4", "Microsoft.Extensions.Primitives": "9.0.4"}, "runtime": {"lib/netcoreapp3.0/Microsoft.Extensions.Logging.EventSource.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "Microsoft.Extensions.Options/9.0.4": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.4", "Microsoft.Extensions.Primitives": "9.0.4"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Options.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}}, "Microsoft.Extensions.Options.ConfigurationExtensions/9.0.4": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.4", "Microsoft.Extensions.Configuration.Binder": "9.0.4", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.4", "Microsoft.Extensions.Options": "9.0.4", "Microsoft.Extensions.Primitives": "9.0.4"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}}, "Microsoft.Extensions.Primitives/9.0.4": {"runtime": {"lib/net8.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}}, "Microsoft.Identity.Client/4.61.3": {"dependencies": {"Microsoft.IdentityModel.Abstractions": "6.35.0", "System.Diagnostics.DiagnosticSource": "9.0.4"}, "runtime": {"lib/net6.0/Microsoft.Identity.Client.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.Identity.Client.Extensions.Msal/4.61.3": {"dependencies": {"Microsoft.Identity.Client": "4.61.3", "System.Security.Cryptography.ProtectedData": "4.7.0"}, "runtime": {"lib/net6.0/Microsoft.Identity.Client.Extensions.Msal.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.IdentityModel.Abstractions/6.35.0": {"runtime": {"lib/net6.0/Microsoft.IdentityModel.Abstractions.dll": {"assemblyVersion": "********", "fileVersion": "6.35.0.41201"}}}, "Microsoft.NETCore.Platforms/5.0.0": {}, "Microsoft.Win32.Registry/5.0.0": {"dependencies": {"System.Security.AccessControl": "5.0.0", "System.Security.Principal.Windows": "5.0.0"}}, "Newtonsoft.Json/13.0.3": {"runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"assemblyVersion": "********", "fileVersion": "13.0.3.27908"}}}, "System.ClientModel/1.1.0": {"dependencies": {"System.Memory.Data": "6.0.0", "System.Text.Json": "6.0.10"}, "runtime": {"lib/net6.0/System.ClientModel.dll": {"assemblyVersion": "*******", "fileVersion": "1.100.24.46703"}}}, "System.Collections.Immutable/5.0.0": {}, "System.Diagnostics.DiagnosticSource/9.0.4": {"runtime": {"lib/net8.0/System.Diagnostics.DiagnosticSource.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}}, "System.Diagnostics.EventLog/5.0.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.Win32.Registry": "5.0.0", "System.Security.Principal.Windows": "5.0.0"}, "runtime": {"lib/netstandard2.0/System.Diagnostics.EventLog.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}, "runtimeTargets": {"runtimes/win/lib/netcoreapp2.0/System.Diagnostics.EventLog.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "System.Memory/4.5.4": {}, "System.Memory.Data/6.0.0": {"dependencies": {"System.Text.Json": "6.0.10"}, "runtime": {"lib/net6.0/System.Memory.Data.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Numerics.Vectors/4.5.0": {}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {}, "System.Security.AccessControl/5.0.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Security.Principal.Windows": "5.0.0"}}, "System.Security.Cryptography.ProtectedData/4.7.0": {"runtime": {"lib/netstandard2.0/System.Security.Cryptography.ProtectedData.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.19.56404"}}, "runtimeTargets": {"runtimes/win/lib/netstandard2.0/System.Security.Cryptography.ProtectedData.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "4.700.19.56404"}}}, "System.Security.Principal.Windows/5.0.0": {}, "System.Text.Encodings.Web/6.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "System.Text.Json/6.0.10": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0", "System.Text.Encodings.Web": "6.0.0"}}, "System.Threading.Tasks.Extensions/4.5.4": {}}}, "libraries": {"CorzaVeevaIntegration.CommonFunctions/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Azure.Core/1.44.1": {"type": "package", "serviceable": true, "sha512": "sha512-YyznXLQZCregzHvioip07/BkzjuWNXogJEVz9T5W6TwjNr17ax41YGzYMptlo2G10oLCuVPoyva62y0SIRDixg==", "path": "azure.core/1.44.1", "hashPath": "azure.core.1.44.1.nupkg.sha512"}, "Azure.Core.Amqp/1.3.1": {"type": "package", "serviceable": true, "sha512": "sha512-AY1ZM4WwLBb9L2WwQoWs7wS2XKYg83tp3yVVdgySdebGN0FuIszuEqCy3Nhv6qHpbkjx/NGuOTsUbF/oNGBgwA==", "path": "azure.core.amqp/1.3.1", "hashPath": "azure.core.amqp.1.3.1.nupkg.sha512"}, "Azure.Identity/1.12.0": {"type": "package", "serviceable": true, "sha512": "sha512-OBIM3aPz8n9oEO5fdnee+Vsc5Nl4W3FeslPpESyDiyByntQI5BAa76KD60eFXm9ulevnwxGZP9YXL8Y+paI5Uw==", "path": "azure.identity/1.12.0", "hashPath": "azure.identity.1.12.0.nupkg.sha512"}, "Azure.Messaging.ServiceBus/7.19.0": {"type": "package", "serviceable": true, "sha512": "sha512-DoGfZpH6bwtsA6siIS5wv3SZZzOoNLnJi9N7OHhtBvE5Wlfn13jvwEe5z92HOddJVYEchdIZBK+jHBtPz1HFpg==", "path": "azure.messaging.servicebus/7.19.0", "hashPath": "azure.messaging.servicebus.7.19.0.nupkg.sha512"}, "Google.Protobuf/3.27.1": {"type": "package", "serviceable": true, "sha512": "sha512-7IVz9TzhYCZ8qY0rPhXUnyJSXYdshUqmmxmTI763XmDDSJJFnyfKH43FFcMJu/CZgBcE98xlFztrKwhzcRkiPg==", "path": "google.protobuf/3.27.1", "hashPath": "google.protobuf.3.27.1.nupkg.sha512"}, "Grpc.Core.Api/2.55.0": {"type": "package", "serviceable": true, "sha512": "sha512-qtJauqAZ4jAM6A2TQyenhpZWPfJBow7/HV/KiRbxJvYHo9jVtUL7SBIs3cjvBh5DXIUaNFIFfBQ3QGyv3nLKCw==", "path": "grpc.core.api/2.55.0", "hashPath": "grpc.core.api.2.55.0.nupkg.sha512"}, "Grpc.Net.Client/2.55.0": {"type": "package", "serviceable": true, "sha512": "sha512-cQyNEAIVs/5npjSHuCKCT5kQJm3AyOnUw7h7s2lXt9rSWC/bhCGPbNAtiFGNvKiQAYgIt4zV13BS983viZP9pg==", "path": "grpc.net.client/2.55.0", "hashPath": "grpc.net.client.2.55.0.nupkg.sha512"}, "Grpc.Net.ClientFactory/2.55.0": {"type": "package", "serviceable": true, "sha512": "sha512-D<PERSON>yiJjFxK2mwy0LqNlT8BhZ2nyKWds+Vh44tL2Ilub8evL/98Zt2m5D/xqteO87Ml8S4vyjXrWgSVEyH00mPSw==", "path": "grpc.net.clientfactory/2.55.0", "hashPath": "grpc.net.clientfactory.2.55.0.nupkg.sha512"}, "Grpc.Net.Common/2.55.0": {"type": "package", "serviceable": true, "sha512": "sha512-s1tzY9Z3jKq+mcYGSkZSTTs8Gwi9M5zfXltSDIx7XvFIdSbh+QtfWiV/4n+vP8yDvFDQASaW+6BzkGUbHEftRA==", "path": "grpc.net.common/2.55.0", "hashPath": "grpc.net.common.2.55.0.nupkg.sha512"}, "Microsoft.Azure.Amqp/2.6.9": {"type": "package", "serviceable": true, "sha512": "sha512-5i9XzfqxK1H5IBl+OuOV1jwJdrOvi5RUwsZgVOryZm0GCzcM9NWPNRxzPAbsSeaR2T6+1gGvdT3vR+Vbha6KFQ==", "path": "microsoft.azure.amqp/2.6.9", "hashPath": "microsoft.azure.amqp.2.6.9.nupkg.sha512"}, "Microsoft.Azure.Functions.Worker.Core/1.19.0": {"type": "package", "serviceable": true, "sha512": "sha512-srGJHtJNJRI/cxBkKSZd9+mA8V8nBbsCx3mwFBZKY2M6/EF3mP8EoUosd4W8AjrKOAEXjgXVEp3K0fQyPXcquA==", "path": "microsoft.azure.functions.worker.core/1.19.0", "hashPath": "microsoft.azure.functions.worker.core.1.19.0.nupkg.sha512"}, "Microsoft.Azure.Functions.Worker.Extensions.Abstractions/1.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-+6+/Yb/ouWUweaSQhesbbiIVSmwYEzkSfjIHrBnNqIiCYnx2iLeoYyWjN/wHP3Fnn5COtyDXRDwHKr5A/tCL9Q==", "path": "microsoft.azure.functions.worker.extensions.abstractions/1.3.0", "hashPath": "microsoft.azure.functions.worker.extensions.abstractions.1.3.0.nupkg.sha512"}, "Microsoft.Azure.Functions.Worker.Extensions.Rpc/1.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-RC1vBEjEU4eu3S1R3b9DljEX0cT84krNNIV2GGl4H2GNtxEDpIw/hO3j5ew+6XQLra6ao/bHFxMTXxFsLr/7Yg==", "path": "microsoft.azure.functions.worker.extensions.rpc/1.0.1", "hashPath": "microsoft.azure.functions.worker.extensions.rpc.1.0.1.nupkg.sha512"}, "Microsoft.Azure.Functions.Worker.Extensions.ServiceBus/5.22.2": {"type": "package", "serviceable": true, "sha512": "sha512-gQrWpglaTKt2TzKCE527eakKhibUviRcr237x/VZl1NojnWThpEGPjNC+1YL+yKAOIQbnHT/yog16pofs8ZCrA==", "path": "microsoft.azure.functions.worker.extensions.servicebus/5.22.2", "hashPath": "microsoft.azure.functions.worker.extensions.servicebus.5.22.2.nupkg.sha512"}, "Microsoft.Bcl.AsyncInterfaces/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-UcSjPsst+DfAdJGVDsu346FX0ci0ah+lw3WRtn18NUwEqRt70HaOQ7lI72vy3+1LxtqI3T5GWwV39rQSrCzAeg==", "path": "microsoft.bcl.asyncinterfaces/6.0.0", "hashPath": "microsoft.bcl.asyncinterfaces.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Azure/1.7.5": {"type": "package", "serviceable": true, "sha512": "sha512-g4yVHO4qlOKEwniz57o4sb/Pl4/ne6o5ecGLJIMp46PdMMIicIFcKPb1+IQNWLPAvg0LxNAeR2qHwdqAA7BKMg==", "path": "microsoft.extensions.azure/1.7.5", "hashPath": "microsoft.extensions.azure.1.7.5.nupkg.sha512"}, "Microsoft.Extensions.Configuration/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-KIVBrMbItnCJDd1RF4KEaE8jZwDJcDUJW5zXpbwQ05HNYTK1GveHxHK0B3SjgDJuR48GRACXAO+BLhL8h34S7g==", "path": "microsoft.extensions.configuration/9.0.4", "hashPath": "microsoft.extensions.configuration.9.0.4.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-0LN/DiIKvBrkqp7gkF3qhGIeZk6/B63PthAHjQsxymJfIBcz0kbf4/p/t4lMgggVxZ+flRi5xvTwlpPOoZk8fg==", "path": "microsoft.extensions.configuration.abstractions/9.0.4", "hashPath": "microsoft.extensions.configuration.abstractions.9.0.4.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Binder/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-cdrjcl9RIcwt3ECbnpP0Gt1+pkjdW90mq5yFYy8D9qRj2NqFFcv3yDp141iEamsd9E218sGxK8WHaIOcrqgDJg==", "path": "microsoft.extensions.configuration.binder/9.0.4", "hashPath": "microsoft.extensions.configuration.binder.9.0.4.nupkg.sha512"}, "Microsoft.Extensions.Configuration.CommandLine/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-OelM+VQdhZ0XMXsEQBq/bt3kFzD+EBGqR4TAgFDRAye0JfvHAaRi+3BxCRcwqUAwDhV0U0HieljBGHlTgYseRA==", "path": "microsoft.extensions.configuration.commandline/5.0.0", "hashPath": "microsoft.extensions.configuration.commandline.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.EnvironmentVariables/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-fqh6y6hAi0Z0fRsb4B/mP9OkKkSlifh5osa+N/YSQ+/S2a//+zYApZMUC1XeP9fdjlgZoPQoZ72Q2eLHyKLddQ==", "path": "microsoft.extensions.configuration.environmentvariables/5.0.0", "hashPath": "microsoft.extensions.configuration.environmentvariables.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.FileExtensions/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-rRdspYKA18ViPOISwAihhCMbusHsARCOtDMwa23f+BGEdIjpKPlhs3LLjmKlxfhpGXBjIsS0JpXcChjRUN+PAw==", "path": "microsoft.extensions.configuration.fileextensions/5.0.0", "hashPath": "microsoft.extensions.configuration.fileextensions.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Json/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Pak8ymSUfdzPfBTLHxeOwcR32YDbuVfhnH2hkfOLnJNQd19ItlBdpMjIDY9C5O/nS2Sn9bzDMai0ZrvF7KyY/Q==", "path": "microsoft.extensions.configuration.json/5.0.0", "hashPath": "microsoft.extensions.configuration.json.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.UserSecrets/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-+tK3seG68106lN277YWQvqmfyI/89w0uTu/5Gz5VYSUu5TI4mqwsaWLlSmT9Bl1yW/i1Nr06gHJxqaqB5NU9Tw==", "path": "microsoft.extensions.configuration.usersecrets/5.0.0", "hashPath": "microsoft.extensions.configuration.usersecrets.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-f2MTUaS2EQ3lX4325ytPAISZqgBfXmY0WvgD80ji6Z20AoDNiCESxsqo6mFRwHJD/jfVKRw9FsW6+86gNre3ug==", "path": "microsoft.extensions.dependencyinjection/9.0.4", "hashPath": "microsoft.extensions.dependencyinjection.9.0.4.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-UI0TQPVkS78bFdjkTodmkH0Fe8lXv9LnhGFKgKrsgUJ5a5FVdFRcgjIkBVLbGgdRhxWirxH/8IXUtEyYJx6GQg==", "path": "microsoft.extensions.dependencyinjection.abstractions/9.0.4", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.9.0.4.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-1bCSQrGv9+bpF5MGKF6THbnRFUZqQDrWPA39NDeVW9djeHBmow8kX4SX6/8KkeKI8gmUDG7jsG/bVuNAcY/ATQ==", "path": "microsoft.extensions.diagnostics/9.0.4", "hashPath": "microsoft.extensions.diagnostics.9.0.4.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics.Abstractions/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-IAucBcHYtiCmMyFag+Vrp5m+cjGRlDttJk9Vx7Dqpq+Ama4BzVUOk0JARQakgFFr7ZTBSgLKlHmtY5MiItB7Cg==", "path": "microsoft.extensions.diagnostics.abstractions/9.0.4", "hashPath": "microsoft.extensions.diagnostics.abstractions.9.0.4.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-iuZIiZ3mteEb+nsUqpGXKx2cGF+cv6gWPd5jqQI4hzqdiJ6I94ddLjKhQOuRW1lueHwocIw30xbSHGhQj0zjdQ==", "path": "microsoft.extensions.fileproviders.abstractions/5.0.0", "hashPath": "microsoft.extensions.fileproviders.abstractions.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Physical/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-1rkd8UO2qf21biwO7X0hL9uHP7vtfmdv/NLvKgCRHkdz1XnW8zVQJXyEYiN68WYpExgtVWn55QF0qBzgfh1mGg==", "path": "microsoft.extensions.fileproviders.physical/5.0.0", "hashPath": "microsoft.extensions.fileproviders.physical.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileSystemGlobbing/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ArliS8lGk8sWRtrWpqI8yUVYJpRruPjCDT+EIjrgkA/AAPRctlAkRISVZ334chAKktTLzD1+PK8F5IZpGedSqA==", "path": "microsoft.extensions.filesystemglobbing/5.0.0", "hashPath": "microsoft.extensions.filesystemglobbing.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.Hosting/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-hiokSU1TOVfcqpQAnpiOzP2rE9p+niq92g5yeAnwlbSrUlIdIS6M8emCknZvhdOagQA9x5YWNwe1n0kFUwE0NQ==", "path": "microsoft.extensions.hosting/5.0.0", "hashPath": "microsoft.extensions.hosting.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.Hosting.Abstractions/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-cbUOCePYBl1UhM+N2zmDSUyJ6cODulbtUd9gEzMFIK3RQDtP/gJsE08oLcBSXH3Q1RAQ0ex7OAB3HeTKB9bXpg==", "path": "microsoft.extensions.hosting.abstractions/5.0.0", "hashPath": "microsoft.extensions.hosting.abstractions.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.Http/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-ezelU6HJgmq4862YoWuEbHGSV+JnfnonTSbNSJVh6n6wDehyiJn4hBtcK7rGbf2KO3QeSvK5y8E7uzn1oaRH5w==", "path": "microsoft.extensions.http/9.0.4", "hashPath": "microsoft.extensions.http.9.0.4.nupkg.sha512"}, "Microsoft.Extensions.Logging/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-xW6QPYsqhbuWBO9/1oA43g/XPKbohJx+7G8FLQgQXIriYvY7s+gxr2wjQJfRoPO900dvvv2vVH7wZovG+M1m6w==", "path": "microsoft.extensions.logging/9.0.4", "hashPath": "microsoft.extensions.logging.9.0.4.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-0MXlimU4Dud6t+iNi5NEz3dO2w1HXdhoOLaYFuLPCjAsvlPQGwOT6V2KZRMLEhCAm/stSZt1AUv0XmDdkjvtbw==", "path": "microsoft.extensions.logging.abstractions/9.0.4", "hashPath": "microsoft.extensions.logging.abstractions.9.0.4.nupkg.sha512"}, "Microsoft.Extensions.Logging.Configuration/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-N3/d0HeMRnBekadbZlmbp+In8EvNNkQHSdbtRzjrGVckdZWpYs5GNrAfaYqVplDFW0WUedSaFJ3khB50BWYGsw==", "path": "microsoft.extensions.logging.configuration/5.0.0", "hashPath": "microsoft.extensions.logging.configuration.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Console/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-jH0wbWhfvXjOVmCkbra4vbiovDtTUIWLQjCeJ7Xun3h4AHvwfzm7V7wlsXKs3tNnPrsCxZ9oaV0vUAgGY1JxOA==", "path": "microsoft.extensions.logging.console/5.0.0", "hashPath": "microsoft.extensions.logging.console.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Debug/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-9dvt0xqRrClvhaPNpfyS39WxnW9G55l5lrV5ZX7IrEgwo4VwtmJKtoPiKVYKbhAuOBGUI5WY3hWLvF+PSbJp5A==", "path": "microsoft.extensions.logging.debug/5.0.0", "hashPath": "microsoft.extensions.logging.debug.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.EventLog/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-CYzsgF2lqgahGl/HuErsIDaZZ9ueN+MBjGfO/0jVDLPaXLaywxlGKFpDgXMaB053DRYZwD1H2Lb1I60mTXS3jg==", "path": "microsoft.extensions.logging.eventlog/5.0.0", "hashPath": "microsoft.extensions.logging.eventlog.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.EventSource/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-hF+D6PJkrM0qXcSEGs1BwZwgP8c0BRkj26P/5wmYTcHKOp52GRey/Z/YKRmRIHIrXxj9tz/JgIjU9oWmiJ5HMw==", "path": "microsoft.extensions.logging.eventsource/5.0.0", "hashPath": "microsoft.extensions.logging.eventsource.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.Options/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-fiFI2+58kicqVZyt/6obqoFwHiab7LC4FkQ3mmiBJ28Yy4fAvy2+v9MRnSvvlOO8chTOjKsdafFl/K9veCPo5g==", "path": "microsoft.extensions.options/9.0.4", "hashPath": "microsoft.extensions.options.9.0.4.nupkg.sha512"}, "Microsoft.Extensions.Options.ConfigurationExtensions/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-aridVhAT3Ep+vsirR1pzjaOw0Jwiob6dc73VFQn2XmDfBA2X98M8YKO1GarvsXRX7gX1Aj+hj2ijMzrMHDOm0A==", "path": "microsoft.extensions.options.configurationextensions/9.0.4", "hashPath": "microsoft.extensions.options.configurationextensions.9.0.4.nupkg.sha512"}, "Microsoft.Extensions.Primitives/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-SPFyMjyku1nqTFFJ928JAMd0QnRe4xjE7KeKnZMWXf3xk+6e0WiOZAluYtLdbJUXtsl2cCRSi8cBquJ408k8RA==", "path": "microsoft.extensions.primitives/9.0.4", "hashPath": "microsoft.extensions.primitives.9.0.4.nupkg.sha512"}, "Microsoft.Identity.Client/4.61.3": {"type": "package", "serviceable": true, "sha512": "sha512-naJo/Qm35Caaoxp5utcw+R8eU8ZtLz2ALh8S+gkekOYQ1oazfCQMWVT4NJ/FnHzdIJlm8dMz0oMpMGCabx5odA==", "path": "microsoft.identity.client/4.61.3", "hashPath": "microsoft.identity.client.4.61.3.nupkg.sha512"}, "Microsoft.Identity.Client.Extensions.Msal/4.61.3": {"type": "package", "serviceable": true, "sha512": "sha512-PWnJcznrSGr25MN8ajlc2XIDW4zCFu0U6FkpaNLEWLgd1NgFCp5uDY3mqLDgM8zCN8hqj8yo5wHYfLB2HjcdGw==", "path": "microsoft.identity.client.extensions.msal/4.61.3", "hashPath": "microsoft.identity.client.extensions.msal.4.61.3.nupkg.sha512"}, "Microsoft.IdentityModel.Abstractions/6.35.0": {"type": "package", "serviceable": true, "sha512": "sha512-xuR8E4Rd96M41CnUSCiOJ2DBh+z+zQSmyrYHdYhD6K4fXBcQGVnRCFQ0efROUYpP+p0zC1BLKr0JRpVuujTZSg==", "path": "microsoft.identitymodel.abstractions/6.35.0", "hashPath": "microsoft.identitymodel.abstractions.6.35.0.nupkg.sha512"}, "Microsoft.NETCore.Platforms/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-VyPlqzH2wavqquTcYpkIIAQ6WdenuKoFN0BdYBbCWsclXacSOHNQn66Gt4z5NBqEYW0FAPm5rlvki9ZiCij5xQ==", "path": "microsoft.netcore.platforms/5.0.0", "hashPath": "microsoft.netcore.platforms.5.0.0.nupkg.sha512"}, "Microsoft.Win32.Registry/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dDoKi0PnDz31yAyETfRntsLArTlVAVzUzCIvvEDsDsucrl33Dl8pIJG06ePTJTI3tGpeyHS9Cq7Foc/s4EeKcg==", "path": "microsoft.win32.registry/5.0.0", "hashPath": "microsoft.win32.registry.5.0.0.nupkg.sha512"}, "Newtonsoft.Json/13.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "path": "newtonsoft.json/13.0.3", "hashPath": "newtonsoft.json.13.0.3.nupkg.sha512"}, "System.ClientModel/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-UocOlCkxLZrG2CKMAAImPcldJTxeesHnHGHwhJ0pNlZEvEXcWKuQvVOER2/NiOkJGRJk978SNdw3j6/7O9H1lg==", "path": "system.clientmodel/1.1.0", "hashPath": "system.clientmodel.1.1.0.nupkg.sha512"}, "System.Collections.Immutable/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-FXkLXiK0sVVewcso0imKQoOxjoPAj42R8HtjjbSjVPAzwDfzoyoznWxgA3c38LDbN9SJux1xXoXYAhz98j7r2g==", "path": "system.collections.immutable/5.0.0", "hashPath": "system.collections.immutable.5.0.0.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-Be0emq8bRmcK4eeJIFUt9+vYPf7kzuQrFs8Ef1CdGvXpq/uSve22PTSkRF09bF/J7wmYJ2DHf2v7GaT3vMXnwQ==", "path": "system.diagnostics.diagnosticsource/9.0.4", "hashPath": "system.diagnostics.diagnosticsource.9.0.4.nupkg.sha512"}, "System.Diagnostics.EventLog/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-FHkCwUfsTs+/5tsK+c0egLfacUgbhvcwi3wUFWSEEArSXao343mYqcpOVVFMlcCkdNtjU4YwAWaKYwal6f02og==", "path": "system.diagnostics.eventlog/5.0.0", "hashPath": "system.diagnostics.eventlog.5.0.0.nupkg.sha512"}, "System.Memory/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-1MbJTHS1lZ4bS4FmsJjnuGJOu88ZzTT2rLvrhW7Ygic+pC0NWA+3hgAen0HRdsocuQXCkUTdFn9yHJJhsijDXw==", "path": "system.memory/4.5.4", "hashPath": "system.memory.4.5.4.nupkg.sha512"}, "System.Memory.Data/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ntFHArH3I4Lpjf5m4DCXQHJuGwWPNVJPaAvM95Jy/u+2Yzt2ryiyIN04LAogkjP9DeRcEOiviAjQotfmPq/FrQ==", "path": "system.memory.data/6.0.0", "hashPath": "system.memory.data.6.0.0.nupkg.sha512"}, "System.Numerics.Vectors/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-QQTlPTl06J/iiDbJCiepZ4H//BVraReU4O4EoRw1U02H5TLUIT7xn3GnDp9AXPSlJUDyFs4uWjWafNX6WrAojQ==", "path": "system.numerics.vectors/4.5.0", "hashPath": "system.numerics.vectors.4.5.0.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "path": "system.runtime.compilerservices.unsafe/6.0.0", "hashPath": "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512"}, "System.Security.AccessControl/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dagJ1mHZO3Ani8GH0PHpPEe/oYO+rVdbQjvjJkBRNQkX4t0r1iaeGn8+/ybkSLEan3/slM0t59SVdHzuHf2jmw==", "path": "system.security.accesscontrol/5.0.0", "hashPath": "system.security.accesscontrol.5.0.0.nupkg.sha512"}, "System.Security.Cryptography.ProtectedData/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-ehYW0m9ptxpGWvE4zgqongBVWpSDU/JCFD4K7krxkQwSz/sFQjEXCUqpvencjy6DYDbn7Ig09R8GFffu8TtneQ==", "path": "system.security.cryptography.protecteddata/4.7.0", "hashPath": "system.security.cryptography.protecteddata.4.7.0.nupkg.sha512"}, "System.Security.Principal.Windows/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-t0MGLukB5WAVU9bO3MGzvlGnyJPgUlcwerXn1kzBRjwLKixT96XV0Uza41W49gVd8zEMFu9vQEFlv0IOrytICA==", "path": "system.security.principal.windows/5.0.0", "hashPath": "system.security.principal.windows.5.0.0.nupkg.sha512"}, "System.Text.Encodings.Web/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Vg8eB5Tawm1IFqj4TVK1czJX89rhFxJo9ELqc/Eiq0eXy13RK00eubyU6TJE6y+GQXjyV5gSfiewDUZjQgSE0w==", "path": "system.text.encodings.web/6.0.0", "hashPath": "system.text.encodings.web.6.0.0.nupkg.sha512"}, "System.Text.Json/6.0.10": {"type": "package", "serviceable": true, "sha512": "sha512-NSB0kDipxn2ychp88NXWfFRFlmi1bst/xynOutbnpEfRCT9JZkZ7KOmF/I/hNKo2dILiMGnqblm+j1sggdLB9g==", "path": "system.text.json/6.0.10", "hashPath": "system.text.json.6.0.10.nupkg.sha512"}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-zteT+G8xuGu6mS+mzDzYXbzS7rd3K6Fjb9RiZlYlJPam2/hU7JCBZBVEcywNuR+oZ1ncTvc/cq0faRr3P01OVg==", "path": "system.threading.tasks.extensions/4.5.4", "hashPath": "system.threading.tasks.extensions.4.5.4.nupkg.sha512"}}}