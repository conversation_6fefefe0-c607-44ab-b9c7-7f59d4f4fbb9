﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using Azure.Messaging.ServiceBus;
using Microsoft.Graph;
using Microsoft.Graph.Models;
using Azure.Identity;
using Microsoft.Graph.Users.Item.SendMail;
using Newtonsoft.Json;

namespace VeevaIntegration
{
    public class FuncCorzaIntegrationHubBatchSendNotifications
    {
        private readonly ILogger<FuncCorzaIntegrationHubBatchSendNotifications> _logger;
        private readonly ServiceBusClient _serviceBusClient;
        private static string clientId = Environment.GetEnvironmentVariable("CorzaIntegrationHubAppRegId");
        private static string tenantId = Environment.GetEnvironmentVariable("CorzaTenantId");
        private static string clientSecret = Environment.GetEnvironmentVariable("CorzaIntegrationHubAppRegSecretId");
        private string emailReceiver = Environment.GetEnvironmentVariable("ReceiverEmail");
        private string myEmail = Environment.GetEnvironmentVariable("SenderEmail");
        private static string deadLetterQueueName = Environment.GetEnvironmentVariable("BatchDLQ");

        private static DateTime _lastRunTime = DateTime.MinValue; // Ensures first run executes immediately

        public FuncCorzaIntegrationHubBatchSendNotifications(ILogger<FuncCorzaIntegrationHubBatchSendNotifications> logger, Func<string, ServiceBusClient> serviceBusClientFactory)
        {
            _logger = logger;
            _serviceBusClient = serviceBusClientFactory("Batch");
        }

        [Function("FuncCorzaIntegrationHubBatchSendNotifications")]
        public async Task Run([TimerTrigger("0 * * * * *")] TimerInfo myTimer) // Runs Every 1 Minute
        {
            try
            {
                int intervalMinutes = int.TryParse(Environment.GetEnvironmentVariable("TimerIntervalMinutesBatches"), out int interval) ? interval : 15; // Default to 15 minutes

                DateTime utcNow = DateTime.UtcNow;
                DateTime nextRunTime = _lastRunTime.AddMinutes(intervalMinutes);
                _logger.LogInformation($"Next scheduled run time: {nextRunTime:yyyy-MM-dd HH:mm:ss UTC} (IST: {TimeZoneInfo.ConvertTimeFromUtc(nextRunTime, TimeZoneInfo.FindSystemTimeZoneById("India Standard Time")):yyyy-MM-dd HH:mm:ss})");

                // First execution should run immediately
                if (_lastRunTime == DateTime.MinValue || utcNow >= nextRunTime)
                {
                    _logger.LogInformation($"Executing at: {utcNow:yyyy-MM-dd HH:mm:ss UTC} (IST: {TimeZoneInfo.ConvertTimeFromUtc(utcNow, TimeZoneInfo.FindSystemTimeZoneById("India Standard Time")):yyyy-MM-dd HH:mm:ss})");

                    // Process Dead-Letter Queue
                    var receiver = _serviceBusClient.CreateReceiver(deadLetterQueueName);
                    List<ServiceBusReceivedMessage> allMessages = new List<ServiceBusReceivedMessage>();
                    int batchSize = 100;

                    while (true)
                    {
                        IReadOnlyList<ServiceBusReceivedMessage> batch = await receiver.PeekMessagesAsync(batchSize);
                        if (batch.Count == 0) break;
                        allMessages.AddRange(batch);
                    }

                    _logger.LogInformation($"Received {allMessages.Count} messages from the Dead-Letter Queue.");

                    if (allMessages.Count > 0)
                    {
                        var emailBodyBuilder = new StringBuilder();
                        emailBodyBuilder.AppendLine("Dead Letter Queue Alert");
                        emailBodyBuilder.AppendLine($"Timestamp: {utcNow:yyyy-MM-dd HH:mm:ss UTC}");
                        emailBodyBuilder.AppendLine();

                        int messageCount = 1;
                        foreach (var message in allMessages)
                        {
                            string messageId = message.MessageId;
                            string errorDescription = message.ApplicationProperties.ContainsKey("ErrorDescription") ? message.ApplicationProperties["ErrorDescription"].ToString() : "N/A";
                            string deadLetterReason = message.ApplicationProperties.ContainsKey("DeadLetterReason") ? message.ApplicationProperties["DeadLetterReason"].ToString() : "N/A";
                            string deadLetterErrorDescription = message.ApplicationProperties.ContainsKey("DeadLetterErrorDescription") ? message.ApplicationProperties["DeadLetterErrorDescription"].ToString() : "N/A";
                            string enqueuedTimeUtc = message.EnqueuedTime.UtcDateTime.ToString("yyyy-MM-dd HH:mm:ss UTC");
                            string messageBody = message.Body.ToString();

                            emailBodyBuilder.AppendLine(new string('-', 150)); // separator
                            emailBodyBuilder.AppendLine($"Batch Notification");
                            emailBodyBuilder.AppendLine($"Message {messageCount}");
                            emailBodyBuilder.AppendLine($"Message ID: {messageId}");
                            emailBodyBuilder.AppendLine($"Enqueued Date and Time (UTC): {enqueuedTimeUtc}");
                            emailBodyBuilder.AppendLine();
                            emailBodyBuilder.AppendLine(new string('*', 100));
                            emailBodyBuilder.AppendLine($"Error Description: {errorDescription}");
                            emailBodyBuilder.AppendLine(new string('*', 100));
                            emailBodyBuilder.AppendLine();
                            emailBodyBuilder.AppendLine($"Dead Letter Reason: {deadLetterReason}");
                            emailBodyBuilder.AppendLine($"Dead Letter Error Description: {deadLetterErrorDescription}");
                            emailBodyBuilder.AppendLine();
                            emailBodyBuilder.AppendLine("Message Body:");
                            if (IsJson(messageBody))
                            {
                                var prettyJson = JsonConvert.SerializeObject(JsonConvert.DeserializeObject(messageBody), Formatting.Indented);
                                emailBodyBuilder.AppendLine(prettyJson);
                            }
                            else
                            {
                                emailBodyBuilder.AppendLine(messageBody); // Just include raw content
                            }
                            emailBodyBuilder.AppendLine();

                            messageCount++;
                        }

                        await SendEmailAsync(emailBodyBuilder.ToString());
                        _logger.LogInformation("Dead letter alert email sent successfully.");
                    }
                    else
                    {
                        _logger.LogInformation("No messages in the dead letter queue.");
                    }

                    _lastRunTime = DateTime.UtcNow; // Update last execution time
                    _logger.LogInformation($"Updated last execution time to: {_lastRunTime:yyyy-MM-dd HH:mm:ss UTC} (IST: {TimeZoneInfo.ConvertTimeFromUtc(_lastRunTime, TimeZoneInfo.FindSystemTimeZoneById("India Standard Time")):yyyy-MM-dd HH:mm:ss})");

                }
                else
                {
                    _logger.LogInformation($"Skipping execution. Next run at: {nextRunTime:yyyy-MM-dd HH:mm:ss UTC} (IST: {TimeZoneInfo.ConvertTimeFromUtc(nextRunTime, TimeZoneInfo.FindSystemTimeZoneById("India Standard Time")):yyyy-MM-dd HH:mm:ss})");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error processing dead letter messages: {ex.Message}");
            }
        }

        private async Task SendEmailAsync(string bodyContent)
        {
            try
            {
                var scopes = new[] { "https://graph.microsoft.com/.default" };
                var clientSecretCredential = new ClientSecretCredential(tenantId, clientId, clientSecret);
                var graphClient = new GraphServiceClient(clientSecretCredential, scopes);

                // Get the receiver emails and split them into a list
                string[] emailReceivers = emailReceiver.Split(',').Select(email => email.Trim()).ToArray();

                // Create recipient list
                List<Recipient> recipients = emailReceivers
                    .Select(email => new Recipient { EmailAddress = new EmailAddress { Address = email } })
                    .ToList();

                // Create the file attachment
                var fileAttachment = new FileAttachment
                {
                    OdataType = "#microsoft.graph.fileAttachment",
                    Name = $"DeadLetterMessages_{DateTime.UtcNow:yyyyMMdd_HHmmss}.txt",
                    ContentBytes = Encoding.UTF8.GetBytes(bodyContent),
                    ContentType = "text/plain"
                };

                var emailMessage = new Message
                {
                    Subject = "Dead Letter Queue Alert",
                    Body = new ItemBody
                    {
                        ContentType = BodyType.Text,
                        Content = "Please find attached document to check Batches dead letter queue details."
                    },
                    ToRecipients = recipients,
                    Attachments = new List<Attachment> { fileAttachment }
                };

                await graphClient.Users[myEmail].SendMail.PostAsync(new SendMailPostRequestBody
                {
                    Message = emailMessage,
                    SaveToSentItems = true
                });

                _logger.LogInformation("Dead letter alert email with attachment sent successfully.");
            }
            catch (Exception ex)
            {
                _logger.LogError($"Failed to send email: {ex.Message}");
            }
        }
        private bool IsJson(string input)
        {
            input = input?.Trim();
            if (string.IsNullOrWhiteSpace(input)) return false;

            if ((input.StartsWith("{") && input.EndsWith("}")) || // Object
                (input.StartsWith("[") && input.EndsWith("]")))   // Array
            {
                try
                {
                    var obj = JsonConvert.DeserializeObject(input);
                    return true;
                }
                catch
                {
                    return false;
                }
            }

            return false;
        }
    }
}
