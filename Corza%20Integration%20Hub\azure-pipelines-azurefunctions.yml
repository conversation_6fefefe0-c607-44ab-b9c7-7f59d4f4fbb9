trigger:
- main

pool:
  vmImage: 'ubuntu-latest'

steps:
- task: UseDotNet@2
  displayName: 'Install .NET Core SDK'
  inputs:
    packageType: 'sdk'
    version: '8.x'
    includePreviewVersions: true

- task: DotNetCoreCLI@2
  displayName: 'Restore NuGet packages'
  inputs:
    azureSubscription: 'IaC_CorzaIntegrationHub_Sub1'
    command: 'restore'
    projects: '**/*.csproj'
    feedsToUse: 'select'

- task: DotNetCoreCLI@2
  displayName: 'Build'
  inputs:
    azureSubscription: 'IaC_CorzaIntegrationHub_Sub1'
    command: 'build'
    projects: '**/*.csproj'
    arguments: '--configuration $(BuildConfiguration)'


- task: DotNetCoreCLI@2
  displayName: Test
  inputs:
    azureSubscription: 'IaC_CorzaIntegrationHub_Sub1'
    command: 'test'
    projects: '$(Parameters.TestProjects)'
    arguments: '--configuration $(BuildConfiguration)'

- task: DotNetCoreCLI@2
  displayName: 'Publish'
  inputs:
    azureSubscription: 'IaC_CorzaIntegrationHub_Sub1'
    command: 'publish'
    publishWebProjects: false
    projects: '**/*.csproj'
    arguments: '--configuration $(BuildConfiguration) --output $(Build.ArtifactStagingDirectory)'

- task: PublishPipelineArtifact@1
  displayName: 'Publish Artifact'
  inputs:
    targetPath: '$(Build.ArtifactStagingDirectory)'
    artifact: 'drop'
