﻿using System;
using System.Net.Http;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace CorzaVeevaIntegration.CommonFunctions
{
    public class FuncCorzaIntegrationHubTokenReuse
    {
        private readonly ILogger<FuncCorzaIntegrationHubTokenReuse> _logger;
        private readonly IHttpClientFactory _httpClientFactory;
        private static VeevaTokenCacheWrapper _cachedToken;
        private static readonly SemaphoreSlim _tokenSemaphore = new(1, 1); // One thread at a time

        public FuncCorzaIntegrationHubTokenReuse(ILogger<FuncCorzaIntegrationHubTokenReuse> logger, IHttpClientFactory httpClientFactory)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _httpClientFactory = httpClientFactory ?? throw new ArgumentNullException(nameof(httpClientFactory));
        }

        public async Task<VeevaAuthResultWrapper> GetValidVeevaTokenAsync()
        {
            var now = DateTime.UtcNow;

            // First Quick Check: Try using cached token without locking
            if (IsTokenValid(now))
            {
                _logger.LogInformation("Using cached Veeva token.");
                UpdateSoftExpirationIfNeeded(now);
                return SuccessResult();
            }

            // Token not usable, acquire lock and double-check
            await _tokenSemaphore.WaitAsync();
            try
            {
                now = DateTime.UtcNow; // refresh time after waiting

                // Second Check inside lock
                if (IsTokenValid(now))
                {
                    _logger.LogInformation("Using cached Veeva token after waiting.");
                    UpdateSoftExpirationIfNeeded(now);
                    return SuccessResult();
                }

                _logger.LogInformation("Requesting new Veeva token.");

                // Fetch new token from Veeva
                var result = await FetchNewTokenAsync(now);
                return result;
            }
            finally
            {
                _tokenSemaphore.Release();
            }
        }

        private bool IsTokenValid(DateTime now)
        {
            if (_cachedToken == null)
                return false;

            var timeSinceSoftExpiration = now - _cachedToken.SoftExpiration;
            var timeUntilHardExpiration = _cachedToken.HardExpiration - now;

            // Token is valid if soft expiration not passed and hard expiration is still safe (>1 hour)
            return timeSinceSoftExpiration.TotalMinutes < 0 && timeUntilHardExpiration.TotalHours > 1;
        }

        private void UpdateSoftExpirationIfNeeded(DateTime now)
        {
            var timeUntilSoftExpiration = _cachedToken.SoftExpiration - now;
            if (timeUntilSoftExpiration.TotalMinutes < 15)
            {
                _cachedToken.SoftExpiration = now.AddMinutes(20); // Extend soft expiration
                _logger.LogInformation("Soft expiration updated.");
            }
        }

        private VeevaAuthResultWrapper SuccessResult()
        {
            return new VeevaAuthResultWrapper
            {
                IsSuccess = true,
                Token = _cachedToken.Token
            };
        }

        private async Task<VeevaAuthResultWrapper> FetchNewTokenAsync(DateTime now)
        {
            // Read from config
            string username = Environment.GetEnvironmentVariable("VeevaUserName");
            string password = Environment.GetEnvironmentVariable("VeevaPassword");
            string vaultDNS = Environment.GetEnvironmentVariable("VeevaAuthVaultDns");
            string version = Environment.GetEnvironmentVariable("VeevaAuthVersion");

            if (string.IsNullOrEmpty(username) || string.IsNullOrEmpty(password) ||
                string.IsNullOrEmpty(vaultDNS) || string.IsNullOrEmpty(version))
            {
                return new VeevaAuthResultWrapper
                {
                    IsSuccess = false,
                    ErrorMessage = "Missing Veeva Vault configuration values."
                };
            }

            var requestUri = $"https://{vaultDNS}/api/{version}/auth";
            var requestData = new Dictionary<string, string>
            {
                { "username", username },
                { "password", password }
            };

            _logger.LogInformation($"Version used for authentication: {version}");
            _logger.LogInformation($"User used for authentication: {username}");
            _logger.LogInformation($"Uri used for authentication: {requestUri}");
            
            var content = new FormUrlEncodedContent(requestData);

            var httpClient = _httpClientFactory.CreateClient();
            var response = await httpClient.PostAsync(requestUri, content);
            var responseString = await response.Content.ReadAsStringAsync();

            if (response.IsSuccessStatusCode)
            {
                var authResponse = JsonConvert.DeserializeObject<VeevaAuthResponseWrapper>(responseString);

                if (authResponse == null)
                {
                    return new VeevaAuthResultWrapper
                    {
                        IsSuccess = false,
                        ErrorMessage = "Failed to parse authentication response."
                    };
                }

                // Check 1: responseStatus = FAILURE
                if (string.Equals(authResponse.ResponseStatus, "FAILURE", StringComparison.OrdinalIgnoreCase))
                {
                    return new VeevaAuthResultWrapper
                    {
                        IsSuccess = false,
                        ErrorMessage = authResponse.ResponseMessage ?? "Unknown error with FAILURE status."
                    };
                }

                // Check 2: errors present even when responseStatus != FAILURE
                if (authResponse.Errors != null && authResponse.Errors.Count > 0)
                {
                    return new VeevaAuthResultWrapper
                    {
                        IsSuccess = false,
                        ErrorMessage = authResponse.Errors[0].Message ?? "Unknown error message."
                    };
                }

                if (string.IsNullOrEmpty(authResponse?.Token))
                {
                    string err = authResponse?.Errors != null && authResponse.Errors.Count > 0
                        ? $"{authResponse.Errors[0].Type}: {authResponse.Errors[0].Message}"
                        : "Authentication response missing token.";

                    return new VeevaAuthResultWrapper
                    {
                        IsSuccess = false,
                        ErrorMessage = err
                    };
                }

                // Save token to cache
                _cachedToken = new VeevaTokenCacheWrapper
                {
                    Token = authResponse.Token,
                    SoftExpiration = now.AddMinutes(20),
                    HardExpiration = now.AddHours(48)
                };

                return new VeevaAuthResultWrapper
                {
                    IsSuccess = true,
                    Token = _cachedToken.Token
                };
            }

            // Authentication API failed
            _logger.LogError($"Authentication failed: {response.StatusCode}, Response: {responseString}");

            return new VeevaAuthResultWrapper
            {
                IsSuccess = false,
                ErrorMessage = $"Authentication failed. Status: {response.StatusCode}, Response: {responseString}"
            };
        }
    }

    // ===== Models =====

    public class VeevaAuthResponseWrapper
    {
        [JsonProperty("sessionId")]
        public string Token { get; set; }

        [JsonProperty("responseStatus")]
        public string ResponseStatus { get; set; }

        [JsonProperty("responseMessage")]
        public string ResponseMessage { get; set; }

        [JsonProperty("errors")]
        public List<VeevaAuthErrorDetail> Errors { get; set; }
    }


    public class VeevaTokenCacheWrapper
    {
        public string Token { get; set; }
        public DateTime SoftExpiration { get; set; }
        public DateTime HardExpiration { get; set; }
    }

    public class VeevaAuthResultWrapper
    {
        public bool IsSuccess { get; set; }
        public string Token { get; set; }
        public string ErrorMessage { get; set; }

        public VeevaAuthResultWrapper() { }

        public VeevaAuthResultWrapper(bool isSuccess, string token, string errorMessage)
        {
            IsSuccess = isSuccess;
            Token = token;
            ErrorMessage = errorMessage;
        }
    }

    public class VeevaAuthErrorDetail
    {
        [JsonProperty("type")]
        public string Type { get; set; }

        [JsonProperty("message")]
        public string Message { get; set; }
    }
}
