param location string = 'centralus'
param tenantId string= subscription().tenantId
// param storageAccountName string = 'validationsa001'

module storageAccount './storage-account.bicep' = {
  name: 'storageModule'
  scope: resourceGroup()
  params: {
    storageName: 'crzintegrationhubvalsa'
    location: location
    tags: {
      environment: 'validation'
    }
    storageSkuName: 'Standard_LRS'
  }
}


// Key Vault deployment
module keyVault './key-vault.bicep' = {
  name: 'keyVaultModule'
  scope: resourceGroup()
  params: {
    keyVaultName: 'crzIntegrationHubvalkv'
    location: location
    skuName: 'standard'
    tenantId: tenantId
    storageAccountName: 'crzintegrationhubvalsa'
    serviceBusNamespace: 'crz-integration-hub-val-sbus'
    appInsightsName: 'crz-integration-hub-val-app-ins'
  }
  dependsOn: [
    storageAccount
    serviceBus
    appInsights
  ]
}

module logAnalytics '../../../../modules/log_analytics/main.bicep' = {
  name: 'logAnalyticsDeploy'
  params: {
    workspaceName: 'crz-integration-hub-val-app-ins-ws'
    location: location
  }
}

module appInsights './app-insights.bicep' = {
  name: 'appInsightsDeploy'
  params: {
    name: 'crz-integration-hub-val-app-ins'
    location: location
    workspaceResourceId: logAnalytics.outputs.workspaceResourceId
  }
}

module serviceBus './service-bus.bicep' = {
  name: 'serviceBusDeploy'
  params: {
    name: 'crz-integration-hub-val-sbus'
    location: location
    topics: [
      'topic_batchchanged_erps'
      'topic_itemchanged_erps'
    ]  
  }
}

module functionApp './function-app.bicep' = {
  name: 'functionAppDeploy'
  params: {
    name: 'func-corza-integration-hub-valv2'
    location: location
    // storageAccountName: storageAccountName
    tags: {
      environment: 'validation'
    }
  }
}
