trigger:
- main

pool:
  vmImage: ubuntu-latest

variables:
  # Default values, can be overridden by the script
  resourceGroupName: ''
  templatefile: ''

parameters:
  - name: environment
    type: string
    displayName: 'Select Environment'
    values:
      - dev
      - validation
      - prod
    default: dev  # Default value is dev

  - name: stageToRun
    type: string
    displayName: 'Select Stage to Run'
    values:
      - Validate
      - Deploy
      - Test
      - Destroy
      
    default: Validate  # Default value is Validate

stages:
  - stage: Validate
    displayName: Validate bicep file
    condition: eq('${{ parameters.stageToRun }}', 'Validate')  # Run only if selected stage is Validate
    jobs:
      - job: Validate
        steps:
        - task: AzureCLI@2
          inputs:
            azureSubscription: 'IaC_CorzaIntegrationHub_Sub1'
            scriptType: 'bash'
            scriptLocation: 'inlineScript'
            inlineScript: |
              echo "Selected Environment: ${{ parameters.environment }}"
              
              # Set resource group and template file path based on environment
              if [ "${{ parameters.environment }}" == "dev" ]; then
                export resourceGroupName="corza-integration-hub-devt-rg"
                export location="centralus"
                export templatefile="bicep/envs/dev/ResourceGroups/rg-1/main.bicep"
              elif [ "${{ parameters.environment }}" == "validation" ]; then
                export resourceGroupName="corza-integration-hub-val-rg"
                export location="centralus"
                export templatefile="bicep/envs/validation/ResourceGroups/rg-val/main.bicep"
              elif [ "${{ parameters.environment }}" == "prod" ]; then
                export resourceGroupName="corza-integration-hub-prod-rg"
                export location="centralus"
                export templatefile="bicep/envs/prod/ResourceGroups/rg-prod/main.bicep"
              fi
              
              echo "Resource Group: $resourceGroupName"
              echo "Template file path: $templatefile"
              
              # Ensure the RG exists (create it if it doesn't)
              # if ! az group show --name $resourceGroupName &> /dev/null; then
              #   echo "Resource group $resourceGroupName does not exist. Creating..."
              #   az deployment sub create \
              #     --location eastus \
              #     --template-file bicep/envs/create-rg.bicep \
              #     --parameters rgName=$resourceGroupName
              # else
              #   echo "Resource group $resourceGroupName already exists."
              # fi
              
              # Check if Resource Group exists
              if ! az group show --name $resourceGroupName &> /dev/null; then
                echo "Resource group $resourceGroupName does not exist. Creating it in $location..."
                az group create --name $resourceGroupName --location $location
              else
                echo "Resource group $resourceGroupName already exists."
              fi
              
              
              
              # Check if the template file exists
              if [ ! -f "$templatefile" ]; then
                echo "Error: Template file does not exist at $templatefile"
                exit 1
              fi
              
              # Validate the template with the specified resource group and template file
              az deployment group what-if --resource-group $resourceGroupName \
              --template-file $templatefile
          displayName: 'Validate deployment'

            

  - stage: Deploy
    displayName: Deploy bicep file
    condition: eq('${{ parameters.stageToRun }}', 'Deploy')  # Run only if selected stage is Deploy
    dependsOn: Validate
    jobs:  
      - job: DeployAzure
        steps:
          - task: AzureCLI@2
            inputs:
              azureSubscription: 'IaC_CorzaIntegrationHub_Sub1'
              scriptType: 'bash'
              scriptLocation: 'inlineScript'
              inlineScript: |
                echo "Selected Environment: ${{ parameters.environment }}"
                
                # Set resource group and template file path based on environment
                if [ "${{ parameters.environment }}" == "dev" ]; then
                  export resourceGroupName="corza-integration-hub-devt-rg"
                  export location="centralus"
                  export templatefile="bicep/envs/dev/ResourceGroups/rg-1/main.bicep"
                elif [ "${{ parameters.environment }}" == "validation" ]; then
                  export resourceGroupName="corza-integration-hub-val-rg"
                  export location="centralus"
                  export templatefile="bicep/envs/validation/ResourceGroups/rg-val/main.bicep"
                elif [ "${{ parameters.environment }}" == "prod" ]; then
                  export resourceGroupName="corza-integration-hub-prod-rg"
                  export location="centralus"
                  export templatefile="bicep/envs/prod/ResourceGroups/rg-prod/main.bicep"
                fi
                
                echo "Resource Group: $resourceGroupName"
                echo "Template file path: $templatefile"
                
                # Ensure the RG exists (create it if it doesn't)
                # if ! az group show --name $resourceGroupName &> /dev/null; then
                #   echo "Resource group $resourceGroupName does not exist. Creating..."
                #   az deployment sub create \
                #     --location eastus \
                #     --template-file bicep/envs/create-rg.bicep \
                #     --parameters rgName=$resourceGroupName
                # else
                # echo "Resource group $resourceGroupName already exists."
                # fi
                
                # Check if Resource Group exists
                if ! az group show --name $resourceGroupName &> /dev/null; then
                  echo "Resource group $resourceGroupName does not exist. Creating it in $location..."
                  az group create --name $resourceGroupName --location $location
                else
                  echo "Resource group $resourceGroupName already exists."
                fi
                
                # Check if the template file exists
                if [ ! -f "$templatefile" ]; then
                   echo "Error: Template file does not exist at $templatefile"
                   exit 1
                fi
                
                # Deploy the template with the specified resource group and template file
                az deployment group create --resource-group $resourceGroupName \
                --template-file $templatefile
            displayName: 'Deploy resources'
  - stage: Test
    displayName: Run Resource Verification Tests
    condition: eq('${{ parameters.stageToRun }}', 'Test')
    jobs:
      - job: ResourceTests
        steps:
        - task: PowerShell@2
          inputs:
            targetType: 'inline'
            script: |
              # Install required Azure PowerShell modules
              Install-Module -Name Az.OperationalInsights -Force -AllowClobber -Scope CurrentUser
              Install-Module -Name Az.ApplicationInsights -Force -AllowClobber -Scope CurrentUser
              Install-Module -Name Az.ServiceBus -Force -AllowClobber -Scope CurrentUser
              Install-Module -Name Az.Functions -Force -AllowClobber -Scope CurrentUser
            errorActionPreference: 'continue'
          displayName: 'Install Required Azure Modules'
        - task: AzurePowerShell@5
          inputs:
            azureSubscription: 'IaC_CorzaIntegrationHub_Sub1'
            ScriptType: 'FilePath'
            ScriptPath: '$(System.DefaultWorkingDirectory)/ResourceVerification.ps1'
            ScriptArguments: '-Environment ${{ parameters.environment }}'
            errorActionPreference: 'continue'
            azurePowerShellVersion: 'LatestVersion'
          displayName: 'Run Resource Verification Tests'
        
        - task: PublishTestResults@2
          inputs:
            testResultsFormat: 'JUnit'
            testResultsFiles: '**/test-results.xml'
            mergeTestResults: true
            testRunTitle: 'Resource Verification Tests - ${{ parameters.environment }}'
          condition: always()
          displayName: 'Publish Test Results'

  - stage: Destroy
    displayName: Destroy deployed resources via Bicep
    condition: eq('${{ parameters.stageToRun }}', 'Destroy')
    jobs:
      - job: DestroyResources
        steps:
          - task: AzureCLI@2
            inputs:
              azureSubscription: 'IaC_CorzaIntegrationHub_Sub1'
              scriptType: 'bash'
              scriptLocation: 'inlineScript'
              inlineScript: |
                echo "Selected Environment: ${{ parameters.environment }}"
                
                if [ "${{ parameters.environment }}" == "dev" ]; then
                  export resourceGroupName="corza-integration-hub-devt-rg"
                  export templatefile="bicep/empty.bicep"
                  export keyVaultName="crzintegrationhubdevtsa"
                elif [ "${{ parameters.environment }}" == "validation" ]; then
                  export resourceGroupName="corza-integration-hub-val-rg"
                  export templatefile="bicep/empty.bicep"
                  export keyVaultName="crzintegrationhubvalsa"
                elif [ "${{ parameters.environment }}" == "prod" ]; then
                  export resourceGroupName="corza-integration-hub-prod-rg"
                  export templatefile="bicep/empty.bicep"
                  export keyVaultName="crzintegrationhubprodsa"
                fi
                
                echo "Destroying resources in $resourceGroupName using $templatefile"
                
                az deployment group create \
                  --resource-group $resourceGroupName \
                  --template-file $templatefile \
                  --mode Complete

                # Wait for a few seconds to ensure Key Vault is in soft-deleted state
                echo "Waiting for Key Vault to be in soft-deleted state..."
                sleep 30
                
                # Purge the Key Vault
                echo "Purging Key Vault: $keyVaultName"
                az keyvault purge --name $keyVaultName || true  
            displayName: 'Destroy Bicep-created resources'
