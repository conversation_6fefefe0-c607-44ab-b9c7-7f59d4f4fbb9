param location string = 'centralus'
param tenantId string= subscription().tenantId
// param storageAccountName string = 'devsa001'

module storageAccount './storage-account.bicep' = {
  name: 'storageModule'
  scope: resourceGroup()
  params: {
    storageName: 'crzintegrationhubdevtsa'
    location: location
    tags: {
      environment: 'dev'
      project: 'modularized-bicep'
    }
    storageSkuName: 'Standard_LRS'
  }
}


// Key Vault deployment
module keyVault './key-vault.bicep' = {
  name: 'keyVaultModule'
  scope: resourceGroup()
  params: {
    keyVaultName: 'crzIntegrationHubDevtkv'
    location: location
    skuName: 'standard'
    tenantId: tenantId
    storageAccountName: 'crzintegrationhubdevtsa'
    serviceBusNamespace: 'crz-integration-hub-devt-sbus'
    appInsightsName: 'crz-integration-hub-devt-app-ins'
  }
  dependsOn: [
    storageAccount
    serviceBus
    appInsights
  ]
}

module logAnalytics '../../../../modules/log_analytics/main.bicep' = {
  name: 'logAnalyticsDeploy'
  params: {
    workspaceName: 'crz-integration-hub-devt-app-ins-ws'
    location: location
  }
}

module appInsights './app-insights.bicep' = {
  name: 'appInsightsDeploy'
  params: {
    name: 'crz-integration-hub-devt-app-ins'
    location: location
    workspaceResourceId: logAnalytics.outputs.workspaceResourceId
  }
}

module serviceBus './service-bus.bicep' = {
  name: 'serviceBusDeploy'
  params: {
    name: 'crz-integration-hub-devt-sbus'
    location: location
    topics: [
      'topic_batchchanged_erps'
      'topic_itemchanged_erps'
    ]
  }
}

module functionApp './function-app.bicep' = {
  name: 'functionAppDeploy'
  params: {
    name: 'func-corza-integration-hub-devtv2'
    location: location
    // storageAccountName: storageAccountName
    tags: {
      environment: 'dev'
    }
  }
  
}
