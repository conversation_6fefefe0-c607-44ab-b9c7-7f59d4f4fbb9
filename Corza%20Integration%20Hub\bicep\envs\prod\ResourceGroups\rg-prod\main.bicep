param location string = 'centralus'
param tenantId string= subscription().tenantId
// param storageAccountName string = 'prodsa001'

module storageAccount './storage-account.bicep' = {
  name: 'storageModule'
  scope: resourceGroup()
  params: {
    storageName: 'crzintegrationhubprodsa'
    location: location
    tags: {
      environment: 'prod'
    }
    storageSkuName: 'Standard_LRS'
  }
}


// Key Vault deployment
module keyVault './key-vault.bicep' = {
  name: 'keyVaultModule'
  scope: resourceGroup()
  params: {
    keyVaultName: 'crzIntegrationHubprodkv'
    location: location
    skuName: 'standard'
    tenantId: tenantId
    storageAccountName: 'crzintegrationhubprodsa'
    serviceBusNamespace: 'crz-integration-hub-prod-sbus'
    appInsightsName: 'crz-integration-hub-prod-app-ins'
  }
  dependsOn: [
    storageAccount
    serviceBus
    appInsights
  ]
}

module logAnalytics '../../../../modules/log_analytics/main.bicep' = {
  name: 'logAnalyticsDeploy'
  params: {
    workspaceName: 'crz-integration-hub-prod-app-ins-ws'
    location: location
  }
}

module appInsights './app-insights.bicep' = {
  name: 'appInsightsDeploy'
  params: {
    name: 'crz-integration-hub-prod-app-ins'
    location: location
    workspaceResourceId: logAnalytics.outputs.workspaceResourceId
  }
}

module serviceBus './service-bus.bicep' = {
  name: 'serviceBusDeploy'
  params: {
    name: 'crz-integration-hub-prod-sbus'
    location: location
    topics: [
      'topic_batchchanged_erps'
      'topic_itemchanged_erps'
    ]  
  }
}

module functionApp './function-app.bicep' = {
  name: 'functionAppDeploy'
  params: {
    name: 'func-corza-integration-hub-prod'
    location: location
    // storageAccountName: storageAccountName
    tags: {
      environment: 'prod'
    }
  }
}
