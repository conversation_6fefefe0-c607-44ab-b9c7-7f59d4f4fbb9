﻿using Azure.Messaging.ServiceBus;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using System;
using Azure.Identity;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace CorzaVeevaIntegration.CommonFunctions
{
    public class FuncCorzaIntegrationHubErrorAlgorithm
    {
        public static async Task HandleErrorsForAction<L>(ServiceBusMessageActions messageActions, ServiceBusReceivedMessage message, ILogger<L> logger, Func<ServiceBusReceivedMessage, Task> action)
        {
            try
            {
                logger.LogInformation($"Processing message with DeliveryCount: {message.DeliveryCount}");

                await action(message);
            }
            catch (Exception ex)
            {
                logger.LogError($"Error processing message: {ex.Message}");

                // Read delay from environment variable
                string delaySecondsStr = Environment.GetEnvironmentVariable("ServiceBusRetryDelaySeconds");

                if (int.TryParse(delaySecondsStr, out int delaySeconds) && delaySeconds > 0)
                {
                    logger.LogInformation($"Delaying for {delaySeconds} seconds before abandoning message.");
                    await Task.Delay(TimeSpan.FromSeconds(delaySeconds));
                }

                await messageActions.AbandonMessageAsync(
                    message,
                    new Dictionary<string, object>
                    {
                        { "ErrorDescription", ex.Message }
                    });

                logger.LogInformation($"Message abandoned with error description: {ex.Message}");
                throw;
            }
        }
    }
}
