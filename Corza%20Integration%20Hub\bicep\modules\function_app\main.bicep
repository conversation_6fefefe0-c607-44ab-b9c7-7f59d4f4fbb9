param name string
param location string
// param storageAccountName string
param appSettings array = []
// param servicePlanId string
param tags object
resource functionApp 'Microsoft.Web/sites@2024-04-01' = {
  name: name
  location: location
  kind: 'functionapp'
  identity:{
    type: 'SystemAssigned'}
  tags:tags  
  properties: {
    siteConfig: {
      netFrameworkVersion: 'v8.0'
      appSettings: concat([
        {
          name: 'FUNCTIONS_EXTENSION_VERSION'
          value: '~4'
        }
        {
          name: 'FUNCTIONS_WORKER_RUNTIME'
          value: 'dotnet-isolated' // or node, python, etc.
        }
      ],appSettings)
    }
    // serverFarmId: servicePlanId
    httpsOnly: true
  }
}

output functionAppPrincipalId string = functionApp.identity.principalId
