﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace CorzaIntegrationProductVariant.Models
{
    public class FuncCorzaIntegrationHubBatchModel
    {
        public string BatchId { get; set; }
        public string Configuration { get; set; }
        public string Entity { get; set; }
        public string ExpirationDate { get; set; }
        public string ManufacturingDate { get; set; }
        public string ProductSrc { get; set; }
        public string ProductTypeGroup { get; set; }
        public string UnitOfMeasure { get; set; }
        public string VendorId { get; set; }
    }

    public class FuncCorzaIntegrationHubBatchWrapper
    {
        public List<string> ParentContextRecordSubjects { get; set; }
        public List<FuncCorzaIntegrationHubBatchModel> Payload { get; set; }
    }
}
