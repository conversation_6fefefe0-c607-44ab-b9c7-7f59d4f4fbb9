using Azure.Messaging.ServiceBus;
using CorzaVeevaIntegration.CommonFunctions;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Builder;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using System;
using System.Net;
using CorzaIntegrationProductVariant;

namespace CorzaIntegrationProductVariant
{
    public class Program
    {
        public static void Main(string[] args)
        {
            var builder = FunctionsApplication.CreateBuilder(args);

            // Configure Functions Web Application
            builder.ConfigureFunctionsWebApplication();

            // Register Dependencies for Dependency Injection
            builder.Services.AddSingleton<FuncCorzaIntegrationHubVeevaAPI>();
            builder.Services.AddSingleton<FuncCorzaIntegrationHubErrorAlgorithm>();
            builder.Services.AddSingleton<FuncCorzaIntegrationHubBusinessUnit>();
            builder.Services.AddSingleton<FuncCorzaIntegrationHubUOM>();
            builder.Services.AddSingleton<FuncCorzaIntegrationHubTokenReuse>();

            // Register Service Bus Client for Dependency Injection
            builder.Services.AddSingleton(sp =>
            {
                var dict = new Dictionary<string, ServiceBusClient>
                {
                    { "Items", new ServiceBusClient(Environment.GetEnvironmentVariable("ItemsTopicConnectionString")) },
                    { "Batch", new ServiceBusClient(Environment.GetEnvironmentVariable("BatchTopicConnectionString")) }
                };

                return new Func<string, ServiceBusClient>(key =>
                {
                    if (dict.TryGetValue(key, out var client))
                        return client;
                    throw new ArgumentException($"No ServiceBusClient registered for key '{key}'");
                });
            });


            // Ensure logging is available
            builder.Services.AddLogging();

            // Optional: Configure Application Insights
            // builder.Services
            //     .AddApplicationInsightsTelemetryWorkerService()
            //     .ConfigureFunctionsApplicationInsights();

            var host = builder.Build();
            host.Run();
        }
    }
}