{"dependencies": {"appInsights1": {"resourceId": "/subscriptions/[parameters('subscriptionId')]/resourceGroups/[parameters('resourceGroupName')]/providers/microsoft.insights/components/crz-integration-hub-devt-app-ins-test", "type": "appInsights.azure", "connectionId": "APPLICATIONINSIGHTS_CONNECTION_STRING"}, "storage1": {"resourceId": "/subscriptions/[parameters('subscriptionId')]/resourceGroups/[parameters('resourceGroupName')]/providers/Microsoft.Storage/storageAccounts/crzinttsatest", "type": "storage.azure", "connectionId": "AzureWebJobsStorage"}}}