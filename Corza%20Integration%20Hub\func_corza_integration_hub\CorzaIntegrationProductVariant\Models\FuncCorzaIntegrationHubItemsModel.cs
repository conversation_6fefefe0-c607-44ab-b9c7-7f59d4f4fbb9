﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace CorzaIntegrationProductVariant.Models
{
    public class FuncCorzaIntegrationHubItemsModel
    {
        public string Configuration { get; set; }
        public string Entity { get; set; }
        public string ProductLifeCycle { get; set; }
        public string ProductSrc { get; set; }
        public string ProductSrcName { get; set; }
        public string ProductStatus { get; set; }
        public string ProductTypeGroup { get; set; }
    }

    public class FuncCorzaIntegrationHubItemsWrapper
    {
        public List<string> ParentContextRecordSubjects { get; set; }
        public List<FuncCorzaIntegrationHubItemsModel> Payload { get; set; }
    }
}
