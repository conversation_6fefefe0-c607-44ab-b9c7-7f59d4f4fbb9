using System;
using System.Text;
using System.Threading.Tasks;
using System.Collections.Generic;
using Microsoft.Extensions.Logging;
using Microsoft.Azure.Functions.Worker;
using Azure.Messaging.ServiceBus;
using Newtonsoft.Json;
using System.Net.Http;
using CorzaVeevaIntegration.CommonFunctions;
using CorzaIntegrationProductVariant.Models;

namespace CorzaIntegrationProductVariant
{
    public class FuncCorzaIntegrationHubItems
    {
        private readonly ILogger<FuncCorzaIntegrationHubItems> _logger;
        private readonly FuncCorzaIntegrationHubTokenReuse _tokenReuse;
        private readonly FuncCorzaIntegrationHubVeevaAPI _veevaApi;
        private readonly FuncCorzaIntegrationHubBusinessUnit _getBusinessUnit;

        public enum ProductType
        {
            FG,
            SF,
            RAWMATL
        }

        // Constructor to inject dependencies
        public FuncCorzaIntegrationHubItems(ILogger<FuncCorzaIntegrationHubItems> logger, FuncCorzaIntegrationHubTokenReuse tokenReuse, FuncCorzaIntegrationHubBusinessUnit businessUnit, FuncCorzaIntegrationHubVeevaAPI veevaApi)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _tokenReuse = tokenReuse ?? throw new ArgumentNullException(nameof(tokenReuse));
            _getBusinessUnit = businessUnit ?? throw new ArgumentNullException(nameof(businessUnit));
            _veevaApi = veevaApi ?? throw new ArgumentNullException(nameof(veevaApi));
        }

        [Function(nameof(FuncCorzaIntegrationHubItems))]
        public async Task Run(
            [ServiceBusTrigger("%ItemsTopicName%", "%ItemsSubscriptionName%", Connection = "ItemsTopicConnectionString")]
            ServiceBusReceivedMessage message,
            ServiceBusMessageActions messageActions)
        {
            await FuncCorzaIntegrationHubErrorAlgorithm.HandleErrorsForAction(messageActions, message, _logger, async (msg) => await ProcessMessage(msg, messageActions));
        }

        // Main method to process the received message
        internal async Task ProcessMessage(ServiceBusReceivedMessage message, ServiceBusMessageActions messageActions)
        {
            string messageBody = Encoding.UTF8.GetString(message.Body);
            _logger.LogInformation($"Received message from F&O: {messageBody}");

            var requestData = ValidateJSON(messageBody);
            _logger.LogInformation("Message Validated");

            string BusinessUnit = _getBusinessUnit.GetBusinessUnit(requestData.Entity);

            if (string.IsNullOrEmpty(BusinessUnit))
            {
                _logger.LogWarning("BusinessUnit is null or empty. Completing message without further processing.");
                await messageActions.CompleteMessageAsync(message);
                return;
            }

            _logger.LogInformation($"Business Unit Received {BusinessUnit}");

            if (!Enum.TryParse<ProductType>(requestData.ProductTypeGroup, out var parsedProductType))
            {
                _logger.LogInformation("Invalid Product Type. Completing message without further processing.");
                await messageActions.CompleteMessageAsync(message);
                return;
            }

            string jsonData = CreateProductJson(parsedProductType, requestData.Configuration, requestData.Entity, requestData.ProductSrc, requestData.ProductSrcName, BusinessUnit);

            string apiUrl = parsedProductType switch
            {
                ProductType.FG => Environment.GetEnvironmentVariable("VeevaApiUrlFg"),
                ProductType.SF => Environment.GetEnvironmentVariable("VeevaApiUrlSf"),
                ProductType.RAWMATL => Environment.GetEnvironmentVariable("VeevaApiUrlRM"),
                _ => throw new ArgumentException($"Unsupported product type: {parsedProductType}")
            };

            _logger.LogInformation($"Generated Json Data : {jsonData}");
            _logger.LogInformation($"Received API URL : {apiUrl}");

            var authResult = await _tokenReuse.GetValidVeevaTokenAsync();

            if (!authResult.IsSuccess || string.IsNullOrEmpty(authResult.Token))
            {
                _logger.LogError($"Authentication Failure: {authResult.ErrorMessage}");
                throw new Exception($"Token not received from Veeva Vault: {authResult.ErrorMessage}");
            }

            var apiResult = await _veevaApi.CallVeevaAPIAsync(apiUrl, authResult.Token, jsonData);

            if (!apiResult.IsSuccess || string.IsNullOrEmpty(apiResult.Data))
            {
                _logger.LogError($"Veeva API Failure: {apiResult.ErrorMessage}");
                throw new Exception($"Veeva API Failure: {apiResult.ErrorMessage}");
            }

            _logger.LogInformation($"Successfully completed the function : {apiResult.Data}");
        }

        // Method to validate JSON payload
        private FuncCorzaIntegrationHubItemsModel ValidateJSON(string message)
        {
            var wrapper = JsonConvert.DeserializeObject<FuncCorzaIntegrationHubItemsWrapper>(message);

            if (wrapper?.Payload == null || wrapper.Payload.Count == 0)
            {
                throw new Exception("Payload is missing or empty.");
            }

            var payloadItem = wrapper.Payload[0];

            var requestData = new FuncCorzaIntegrationHubItemsModel
            {
                Configuration = payloadItem.Configuration,
                Entity = payloadItem.Entity,
                ProductLifeCycle = payloadItem.ProductLifeCycle,
                ProductSrc = payloadItem.ProductSrc,
                ProductSrcName = payloadItem.ProductSrcName,
                ProductStatus = payloadItem.ProductStatus,
                ProductTypeGroup = payloadItem.ProductTypeGroup
            };

            if (string.IsNullOrEmpty(requestData.Entity) ||
                string.IsNullOrEmpty(requestData.ProductSrc) ||
                string.IsNullOrEmpty(requestData.ProductSrcName) ||
                string.IsNullOrEmpty(requestData.ProductTypeGroup))
            {
                string missingField = IdentifyMissingField(requestData);
                throw new Exception($"Invalid or missing JSON data: {missingField}");
            }

            return requestData;
        }

        // Helper method to identify missing fields in JSON
        private string IdentifyMissingField(FuncCorzaIntegrationHubItemsModel data)
        {
            if (string.IsNullOrEmpty(data.Entity)) return "Entity";
            if (string.IsNullOrEmpty(data.ProductSrc)) return "ProductSrc";
            if (string.IsNullOrEmpty(data.ProductSrcName)) return "ProductSrcName";
            if (string.IsNullOrEmpty(data.ProductTypeGroup)) return "ProductTypeGroup";
            return "Unknown field";
        }

        // Method to create JSON 
        private string CreateProductJson(ProductType productType, string Configuration, string Entity, string ProductSrc, string ProductSrcName, string business_unit__c)
        {
            var item = new Dictionary<string, object>();

            switch (productType)
            {
                case ProductType.FG:
                    item = new Dictionary<string, object>
                    {
                        { "name__v", $"{ProductSrc}::{Configuration}::{Entity}" },
                        { "business_unit__c", business_unit__c },
                        { "owning_area__c.external_id__v", $"IS-{Entity}" },
                        { "external_id__c", $"{ProductSrc}::{Configuration}::{Entity}" },
                        { "sku__c", ProductSrc },
                        { "configuration__c", Configuration },
                        { "sku_description__c", ProductSrcName },
                        { "entity__c", Entity }
                    };
                    break;

                case ProductType.SF:
                    item = new Dictionary<string, object>
                    {
                        { "name__v", ProductSrc },
                        { "business_unit__c", business_unit__c },
                        { "owning_area__c.external_id__v", $"IS-{Entity}" },
                        { "external_id__c", $"{ProductSrc}::{Entity}" },
                        { "part_description__c", ProductSrcName },
                        { "entity__c", Entity }
                    };
                    break;

                case ProductType.RAWMATL:
                    item = new Dictionary<string, object>
                    {
                        { "name__v", $"{ProductSrc}::{Entity}" },
                        { "business_unit__c", business_unit__c },
                        { "organization__v.external_id__v", $"IS-{Entity}" },
                        { "external_id__v", $"{ProductSrc}::{Entity}" },
                        { "description__v", ProductSrcName },
                        { "material_source__v", "external_supplier__v" },
                        { "entity__c", Entity }
                    };
                    break;

                default:
                    throw new ArgumentException($"Unsupported product type: {productType}");
            }

            var data = new List<Dictionary<string, object>> { item };
            return JsonConvert.SerializeObject(data, Formatting.Indented);
        }
    }
}
