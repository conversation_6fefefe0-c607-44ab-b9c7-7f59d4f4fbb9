using System;
using System.Text;
using System.Threading.Tasks;
using System.Collections.Generic;
using Microsoft.Extensions.Logging;
using Microsoft.Azure.Functions.Worker;
using Azure.Messaging.ServiceBus;
using Newtonsoft.Json;
using System.Net.Http;
using CorzaVeevaIntegration.CommonFunctions;
using CorzaIntegrationProductVariant.Models;

namespace CorzaIntegrationProductVariant
{
    public class FuncCorzaIntegrationHubBatch
    {
        private readonly ILogger<FuncCorzaIntegrationHubBatch> _logger;
        private readonly FuncCorzaIntegrationHubVeevaAPI _veevaApi;
        private readonly FuncCorzaIntegrationHubTokenReuse _tokenReuse;
        private readonly FuncCorzaIntegrationHubBusinessUnit _getBusinessUnit;
        private readonly FuncCorzaIntegrationHubUOM _getUOM;

        public enum ProductTypeGroup
        {
            FG,
            SF,
            RAWMATL,
            Unknown
        }

        public enum ObjectType
        {
            [JsonProperty("finished_product__c")]
            FinishedProduct,

            [JsonProperty("bulk_material__c")]
            BulkMaterial,

            [JsonProperty("raw_material__c")]
            RawMaterial,

            [JsonProperty("unknown_product_type")]
            Unknown
        }

        // Constructor to inject dependencies
        public FuncCorzaIntegrationHubBatch(ILogger<FuncCorzaIntegrationHubBatch> logger,
            FuncCorzaIntegrationHubTokenReuse tokenReuse,
            FuncCorzaIntegrationHubUOM uom,
            FuncCorzaIntegrationHubBusinessUnit businessUnit,
            FuncCorzaIntegrationHubVeevaAPI veevaApi)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _tokenReuse = tokenReuse ?? throw new ArgumentNullException(nameof(tokenReuse));
            _veevaApi = veevaApi ?? throw new ArgumentNullException(nameof(veevaApi));
            _getBusinessUnit = businessUnit ?? throw new ArgumentNullException(nameof(businessUnit));
            _getUOM = uom ?? throw new ArgumentNullException(nameof(uom));
        }

        [Function(nameof(FuncCorzaIntegrationHubBatch))]
        public async Task Run(
            [ServiceBusTrigger("%BatchTopicName%", "%BatchSubscriptionName%", Connection = "BatchTopicConnectionString")]
            ServiceBusReceivedMessage message,
            ServiceBusMessageActions messageActions)
        {
            await FuncCorzaIntegrationHubErrorAlgorithm.HandleErrorsForAction(messageActions, message, _logger,
                async (msg) => await ProcessMessage(msg, messageActions));
        }

        internal async Task ProcessMessage(ServiceBusReceivedMessage message, ServiceBusMessageActions messageActions)
        {
            string messageBody = Encoding.UTF8.GetString(message.Body);
            _logger.LogInformation($"Received message from F&O: {messageBody}");

            var requestData = ValidateJSON(messageBody);
            _logger.LogInformation("Message Validated");

            string BusinessUnit = _getBusinessUnit.GetBusinessUnit(requestData.Entity);

            if (string.IsNullOrEmpty(BusinessUnit))
            {
                _logger.LogWarning("BusinessUnit is null or empty. Completing message without further processing.");
                await messageActions.CompleteMessageAsync(message);
                return;
            }

            _logger.LogInformation($"Business Unit Received {BusinessUnit}");

            string jsonData = string.Empty;

            if (!Enum.TryParse<ProductTypeGroup>(requestData.ProductTypeGroup, out var productType))
            {
                productType = ProductTypeGroup.Unknown;
            }

            var objectType = GetObjectType(productType);

            if (objectType == ObjectType.Unknown)
            {
                _logger.LogInformation("Invalid Product Type. Completing message without further processing.");
                await messageActions.CompleteMessageAsync(message);
                return;
            }

            string UOM = _getUOM.GetUOM(requestData.UnitOfMeasure);

            if (string.IsNullOrEmpty(UOM))
            {
                throw new Exception("Invalid Unit Of Measure");
            }

            jsonData = CreateJsonData(requestData, BusinessUnit, objectType, productType, UOM);

            _logger.LogInformation($"Genrated JSON Data : {jsonData}");

            var authResult = await _tokenReuse.GetValidVeevaTokenAsync();

            if (!authResult.IsSuccess || string.IsNullOrEmpty(authResult.Token))
            {
                _logger.LogError($"Authentication Failure: {authResult.ErrorMessage}");
                throw new Exception($"Token not Received from Veeva Vault: {authResult.ErrorMessage}");
            }

            string apiUrl = Environment.GetEnvironmentVariable("VeevaApiUrlBatch");

            var apiResult = await _veevaApi.CallVeevaAPIAsync(apiUrl, authResult.Token, jsonData);

            if (!apiResult.IsSuccess || string.IsNullOrEmpty(apiResult.Data))
            {
                _logger.LogError($"VeevaAPI Failure: {apiResult.ErrorMessage}");
                throw new Exception($"Veeva API Failure: {apiResult.ErrorMessage}");
            }

            _logger.LogInformation($"Successfully completed the function : {apiResult.Data}");
        }

        private FuncCorzaIntegrationHubBatchModel ValidateJSON(string message)
        {
            var wrapper = JsonConvert.DeserializeObject<FuncCorzaIntegrationHubBatchWrapper>(message);

            if (wrapper?.Payload == null || wrapper.Payload.Count == 0)
            {
                throw new Exception("Payload is missing or empty.");
            }

            var payloadItem = wrapper.Payload[0];

            var requestData = new FuncCorzaIntegrationHubBatchModel
            {
                BatchId = payloadItem.BatchId,
                Configuration = payloadItem.Configuration,
                Entity = payloadItem.Entity,
                ExpirationDate = payloadItem.ExpirationDate,
                ManufacturingDate = payloadItem.ManufacturingDate,
                ProductSrc = payloadItem.ProductSrc,
                ProductTypeGroup = payloadItem.ProductTypeGroup,
                UnitOfMeasure = payloadItem.UnitOfMeasure,
                VendorId = payloadItem.VendorId
            };

            if (string.IsNullOrEmpty(requestData.BatchId) ||
                string.IsNullOrEmpty(requestData.Entity) ||
                string.IsNullOrEmpty(requestData.ProductSrc) ||
                string.IsNullOrEmpty(requestData.ProductTypeGroup) ||
                string.IsNullOrEmpty(requestData.UnitOfMeasure))
            {
                string missingField = IdentifyMissingField(requestData);
                throw new Exception($"Invalid or missing JSON data: {missingField}");
            }

            return requestData;
        }

        private string IdentifyMissingField(FuncCorzaIntegrationHubBatchModel data)
        {
            if (string.IsNullOrEmpty(data.BatchId)) return "BatchId";
            if (string.IsNullOrEmpty(data.Entity)) return "Entity";
            if (string.IsNullOrEmpty(data.ProductSrc)) return "ProductSrc";
            if (string.IsNullOrEmpty(data.ProductTypeGroup)) return "ProductTypeGroup";
            if (string.IsNullOrEmpty(data.UnitOfMeasure)) return "UnitOfMeasure";
            return "Unknown field";
        }

        private string CreateJsonData(FuncCorzaIntegrationHubBatchModel requestData, string businessUnit, ObjectType objectType, ProductTypeGroup productType, string UOM)
        {
            var item = new Dictionary<string, object>();

            switch (productType)
            {
                case ProductTypeGroup.FG:
                    item = new Dictionary<string, object>
                    {
                        { "object_type__v.api_name__v", GetObjectTypeString(objectType) },
                        { "external_batch_id__v", $"{requestData.BatchId}::{requestData.ProductSrc}::{requestData.Entity}" },
                        { "organization__v.external_id__v", $"IS-{requestData.Entity}" },
                        { "manufacture_date__v", requestData.ManufacturingDate },
                        { "expiration_date__v", requestData.ExpirationDate },
                        { "product_variant__v.external_id__c", $"{requestData.ProductSrc}::{requestData.Configuration}::{requestData.Entity}" },
                        { "batch__c", requestData.BatchId },
                        { "business_unit__c", businessUnit },
                        { "owning_area__c.external_id__v", $"IS-{requestData.Entity}" },
                        { "unit_of_measure__c", UOM }
                    };
                    break;

                case ProductTypeGroup.SF:
                    item = new Dictionary<string, object>
                    {
                        { "object_type__v.api_name__v", GetObjectTypeString(objectType) },
                        { "external_batch_id__v", $"{requestData.BatchId}::{requestData.ProductSrc}::{requestData.Entity}" },
                        { "organization__v.external_id__v", $"IS-{requestData.Entity}" },
                        { "manufacture_date__v", requestData.ManufacturingDate },
                        { "expiration_date__v", requestData.ExpirationDate },
                        { "semifinished_goods__c.external_id__c", $"{requestData.ProductSrc}::{requestData.Entity}" },
                        { "batch__c", requestData.BatchId },
                        { "business_unit__c", businessUnit },
                        { "owning_area__c.external_id__v", $"IS-{requestData.Entity}" },
                        { "unit_of_measure__c", UOM }
                    };
                    break;

                case ProductTypeGroup.RAWMATL:
                    item = new Dictionary<string, object>
                    {
                        { "__name__v", requestData.ProductSrc },
                        { "object_type__v.api_name__v", GetObjectTypeString(objectType) },
                        { "__lifecycle__v", "quality_batch_lifecycle__v" },
                        { "__state__v", "active_state__v" },
                        { "external_batch_id__v", $"{requestData.BatchId}::{requestData.ProductSrc}::{requestData.Entity}" },
                        { "organization__v.external_id__v", $"IS-{requestData.Entity}" },
                        { "manufacture_date__v", requestData.ManufacturingDate },
                        { "expiration_date__v", requestData.ExpirationDate },
                        { "material__v.external_id__v", $"{requestData.ProductSrc}::{requestData.Entity}" },
                        { "batch__c", requestData.BatchId },
                        { "business_unit__c", businessUnit },
                        { "owning_area__c.external_id__v", $"IS-{requestData.Entity}" },
                        { "unit_of_measure__c", UOM }
                    };
                    break;

                default:
                    throw new ArgumentOutOfRangeException(nameof(productType), $"Unknown product type: {productType}");
            }

            var data = new List<Dictionary<string, object>> { item };
            return JsonConvert.SerializeObject(data, Formatting.Indented);
        }

        private ObjectType GetObjectType(ProductTypeGroup productTypeGroup)
        {
            return productTypeGroup switch
            {
                ProductTypeGroup.RAWMATL => ObjectType.RawMaterial,
                ProductTypeGroup.FG => ObjectType.FinishedProduct,
                ProductTypeGroup.SF => ObjectType.BulkMaterial,
                _ => ObjectType.Unknown
            };
        }

        private string GetObjectTypeString(ObjectType objectType)
        {
            var fieldInfo = objectType.GetType().GetField(objectType.ToString());
            var attribute = (JsonPropertyAttribute)Attribute.GetCustomAttribute(fieldInfo, typeof(JsonPropertyAttribute));
            return attribute?.PropertyName ?? "unknown_product_type";
        }
    }
}