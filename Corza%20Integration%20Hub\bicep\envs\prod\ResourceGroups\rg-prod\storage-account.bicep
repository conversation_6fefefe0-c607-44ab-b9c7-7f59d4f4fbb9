@description('Azure region of the deployment')
param location string = resourceGroup().location

@description('Tags to add to the resources')
param tags object = {}

@description('Name of the storage account')
param storageName string

@description('Storage SKU')
@allowed([
  'Standard_LRS'
  'Standard_GRS'
  'Standard_ZRS'
  'Premium_LRS'
])
param storageSkuName string = 'Standard_LRS'

module storageModule '../../../../modules/storage_account/main.bicep' = {
  name: 'storageAccountDeployment'
  scope: resourceGroup()
  params: {
    location: location
    tags: tags
    storageName: storageName
    storageSkuName: storageSkuName
  }
}
