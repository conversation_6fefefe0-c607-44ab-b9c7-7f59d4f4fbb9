param name string
param location string
// param storageAccountName string
param keyVaultName string = 'crzIntegrationHubDevtkv'
// var roleAssignmentName = guid(resourceGroup().id, name, 'Key Vault Reader')
param tags object = {}
var devAppSettings = [
  // Application Insights Settings
  {
    name: 'APPINSIGHTS_INSTRUMENTATIONKEY'
    value: '@Microsoft.KeyVault(SecretUri=https://${keyVaultName}.vault.azure.net/secrets/applicationinsights-instrumentation-key)'
  }
  {
    name: 'APPLICATIONINSIGHTS_CONNECTION_STRING'
    value: '@Microsoft.KeyVault(SecretUri=https://${keyVaultName}.vault.azure.net/secrets/applicationinsights-connection-string)'
  }
  
  // Azure AD Authentication Settings
  {
    name: 'CorzaIntegrationHubAppRegId'
    value: '@Microsoft.KeyVault(SecretUri=https://${keyVaultName}.vault.azure.net/secrets/crz-integration-hub-app-id)'
  }
  {
    name: 'CorzaIntegrationHubAppRegSecretId'
    value: '@Microsoft.KeyVault(SecretUri=https://${keyVaultName}.vault.azure.net/secrets/crz-integration-hub-secret-id)'
  }
  {
    name: 'CorzaTenantId'
    value: '@Microsoft.KeyVault(SecretUri=https://${keyVaultName}.vault.azure.net/secrets/AzureTenantId)'
  }

  // Function Disable/Enable Settings
  {
    name: 'AzureWebJobs.func_corza_integration_hub_fg_SendNotifications.Disabled'
    value: '1'
  }
  {
    name: 'AzureWebJobs.func_corza_integration_hub_fg.Disabled'
    value: '0'
  }
  {
    name: 'AzureWebJobs.FunccorzaIntegrationHubBatch.Disabled'
    value: '0'
  }
  {
    name: 'AzureWebJobs.FunccorzaIntegrationHubFg.Disabled'
    value: '1'
  }
  {
    name: 'AzureWebJobs.FunccorzaIntegrationHubFgSendNotifications.Disabled'
    value: '0'
  }
  {
    name: 'AzureWebJobs.FunccorzaIntegrationHubItems.Disabled'
    value: '0'
  }
  {
    name: 'AzureWebJobs.FunccorzaIntegrationHubItemsSendNotifications.Disabled'
    value: '0'
  }
  // Batch Processing Settings
  {
    name: 'BatchDLQ'
    value: 'topic_batchchanged_erps/Subscriptions/subs_batchchanged_veeva/$DeadLetterQueue'
  }
  {
    name: 'BatchSubscriptionName'
    value: 'subs_batchchanged_veeva'
  }
  {
    name: 'BatchTopicConnectionString'
    value: '@Microsoft.KeyVault(SecretUri=https://${keyVaultName}.vault.azure.net/secrets/cihub-sbus-topic-batchchanged-erps-connection-string)'
  }
  {
    name: 'BatchTopicName'
    value: 'topic_batchchanged_erps'
  }

  // Entity and Unit Mapping
  {
    name: 'EntityMapping'
    value: '{   "220": "wound_closure__c",   "156": "ophthalmology__c",   "166": "blink__c",   "240": "oem__c" }'
  }
  {
    name: 'UnitMapping'
    value: '{   "Bag": "bag__c",   "Bag100": "bag100__c",   "Box": "box__c",   "Box10": "box10__c",   "cm": "cm__c",   "ea": "ea__c",   "ft": "ft__c",   "g": "g__c",   "gal": "gal__c",   "in": "in__c",   "kg": "kg__c",   "KM": "km__c",   "l": "l__c",   "lb": "lb__c",   "m": "m__c",   "mg": "mg__c",   "ml": "ml__c",   "mm": "mm__c",   "oz": "oz__c",   "Pint": "pint__c",   "Reel": "reel__c",   "Roll": "roll__c",   "X1000": "x1000__c",   "Box5": "box5__c",   "Box6": "box6__c",   "Box8": "box8__c",   "Box12": "box12__c",   "Box15": "box15__c",   "Box18": "box18__c",   "Box20": "box20__c",   "Box25": "box25__c",   "Box30": "box30__c",   "Box35": "box35__c",   "Box40": "box40__c",   "Box50": "box50__c",   "Box60": "box60__c",   "Box100": "box100__c",   "Pack": "package__c" }'
  }
  
  // Items Processing Settings
  {
    name: 'ItemsDLQ'
    value: 'topic_itemchanged_erps/Subscriptions/subs_itemchanged_veeva/$DeadLetterQueue'
  }
  {
    name: 'ItemsSubscriptionName'
    value: 'subs_itemchanged_veeva'
  }
  {
    name: 'ItemsTopicConnectionString'
    value: '@Microsoft.KeyVault(SecretUri=https://${keyVaultName}.vault.azure.net/secrets/cihub-sbus-topic-itemchanged-erps-connection-string)'
  }
  {
    name: 'ItemsTopicName'
    value: 'topic_itemchanged_erps'
  }

  // Email Settings
  {
    name: 'VeevaPassword'
    value: '@Microsoft.KeyVault(SecretUri=https://${keyVaultName}.vault.azure.net/secrets/veeva-password-secret)'
  }
  {
    name: 'ReceiverEmail'
    value: '<EMAIL>'
  }
  {
    name: 'SenderEmail'
    value: '<EMAIL>'
  }

  // Timer Settings
  {
    name: 'TimerIntervalMinutesBatches'
    value: '1'
  }
  {
    name: 'TimerIntervalMinutesItems'
    value: '1'
  }

  // User Authentication
  {
    name: 'VeevaUserName'
    value: '<EMAIL>'
  }

  // Veeva API Settings
  {
    name: 'VeevaAuthVaultDns'
    value: 'sb-corza-integration-testing-sbx.veevavault.com'
  }
  {
    name: 'VeevaApiUrlBatch'
    value: 'https://sb-corza-integration-testing-sbx.veevavault.com/api/v23.2/vobjects/quality_batch__v?idParam=external_batch_id__v'
  }
  {
    name: 'VeevaApiUrlFg'
    value: 'https://sb-corza-integration-testing-sbx.veevavault.com/api/v24.3/vobjects/product_variant__v?idParam=external_id__c'
  }
  {
    name: 'VeevaApiUrlRM'
    value: 'https://sb-corza-integration-testing-sbx.veevavault.com/api/v23.2/vobjects/quality_material__v?idParam=external_id__v'
  }
  {
    name: 'VeevaApiUrlSf'
    value: 'https://sb-corza-integration-testing-sbx.veevavault.com/api/v24.3/vobjects/part__v?idParam=external_id__c'
  }

  // Version Control
  {
    name: 'VeevaAuthVersion'
    value: 'v24.3'
  }

  // Website Settings
  {
    name: 'WEBSITE_CONTENTAZUREFILECONNECTIONSTRING'
    value: '@Microsoft.KeyVault(SecretUri=https://${keyVaultName}.vault.azure.net/secrets/cihub-storage-account-connection-string)'
  }
  {
    name: 'WEBSITE_CONTENTSHARE'
    value: 'func-corza-integration-huba281'
  }
  {
    name: 'WEBSITE_RUN_FROM_PACKAGE'
    value: '1'
  }
  {
    name: 'WEBSITE_USE_PLACEHOLDER_DOTNETISOLATED'
    value: '1'
  }
  {
    name: 'AzureWebJobsStorage'
    value: '@Microsoft.KeyVault(SecretUri=https://${keyVaultName}.vault.azure.net/secrets/cihub-storage-account-connection-string)'
  }
  {
    name: 'ServiceBusRetryDelaySeconds'
    value: '10'
  }
]

module faModule '../../../../modules/function_app/main.bicep' = {
  name: 'faModule'
  params: {
    name: name
    location: location
    // storageAccountName: storageAccountName
    appSettings: devAppSettings
    tags:tags
  }
}

resource keyVaultAccessPolicy 'Microsoft.KeyVault/vaults/accessPolicies@2023-07-01' = {
  name: '${keyVaultName}/add'
  properties: {
    accessPolicies: [
      {
        tenantId: subscription().tenantId
        objectId: faModule.outputs.functionAppPrincipalId
        permissions: {
          secrets: [
            'get'
            'list'
          ]
          keys: [
            'get'
            'list'
          ]
        }
      }
    ]
  }
}

// Key Vault Reader Role Assignment
// resource keyVaultReaderRoleAssignment 'Microsoft.Authorization/roleAssignments@2022-04-01' = {
//   name: roleAssignmentName
//   scope: resourceGroup()
//   properties: {
//     principalId: faModule.outputs.functionAppPrincipalId
//     roleDefinitionId: subscriptionResourceId('Microsoft.Authorization/roleDefinitions', '********-7ca7-4776-b22c-e363652d74d2') // Key Vault Reader role ID
//     principalType: 'ServicePrincipal'
//   }
// }
