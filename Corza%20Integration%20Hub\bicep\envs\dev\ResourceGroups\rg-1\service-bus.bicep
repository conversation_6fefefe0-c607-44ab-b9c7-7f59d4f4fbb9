param name string
param location string
// param queues array
param topics array

module sbModule '../../../../modules/servicebus/main.bicep' = {
  name: 'sbModule'
  params: {
    name: name
    location: location
    // queues: queues
    topics: topics
  }
}

// Namespace level SAS Policy
resource namespaceSasPolicy 'Microsoft.ServiceBus/namespaces/authorizationRules@2022-10-01-preview' = {
  name: '${name}/crz-integration-hub-devt-sbus-SAS'
  properties: {
    rights: [
      'Listen'
      'Send'
    ]
  }
  dependsOn: [
    sbModule
  ]
}

// Batch Topic SAS Policy
resource batchTopicAuthRule 'Microsoft.ServiceBus/namespaces/topics/authorizationRules@2022-10-01-preview' = {
  name: '${name}/topic_batchchanged_erps/topic_batchchanged_erps-SAS'
  properties: {
    rights: [
      'Send'
      'Listen'
    ]
  }
  dependsOn: [
    sbModule
  ]
}

// Items Topic SAS Policy
resource itemsTopicAuthRule 'Microsoft.ServiceBus/namespaces/topics/authorizationRules@2022-10-01-preview' = {
  name: '${name}/topic_itemchanged_erps/topic_itemchanged_erps-SAS'
  properties: {
    rights: [
      'Send'
      'Listen'
    ]
  }
  dependsOn: [
    sbModule
  ]
}

resource batchSubscription 'Microsoft.ServiceBus/namespaces/topics/subscriptions@2022-10-01-preview' = {
  name: '${name}/topic_batchchanged_erps/subs_batchchanged_veeva'
  properties: {
    maxDeliveryCount: 5
    deadLetteringOnMessageExpiration: true
    defaultMessageTimeToLive: 'P14D'
  }
  dependsOn: [
    sbModule
  ]
}

resource itemsSubscription 'Microsoft.ServiceBus/namespaces/topics/subscriptions@2022-10-01-preview' = {
  name: '${name}/topic_itemchanged_erps/subs_itemchanged_veeva'
  properties: {
    maxDeliveryCount: 5
    deadLetteringOnMessageExpiration: true
    defaultMessageTimeToLive: 'P14D'
  }
  dependsOn: [
    sbModule
  ]
}

