﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;

namespace CorzaVeevaIntegration.CommonFunctions
{
    public class FuncCorzaIntegrationHubBusinessUnit
    {
        public string GetBusinessUnit(string key)
        {
            string json = Environment.GetEnvironmentVariable("EntityMapping");

            var dict = JsonSerializer.Deserialize<Dictionary<string, string>>(json);

            if (dict != null && dict.TryGetValue(key, out string value))
            {
                return value;
            }

            return null;
        }
    }
}
