@description('Name of the Log Analytics workspace')
param workspaceName string

@description('Location of the workspace')
param location string = resourceGroup().location

@description('Tags for the workspace')
param tags object = {}

resource workspace 'Microsoft.OperationalInsights/workspaces@2021-06-01' = {
  name: workspaceName
  location: location
  tags: tags
  properties: {
    retentionInDays: 30
    sku: {
      name: 'PerGB2018'
    }
  }
}

output workspaceResourceId string = workspace.id
