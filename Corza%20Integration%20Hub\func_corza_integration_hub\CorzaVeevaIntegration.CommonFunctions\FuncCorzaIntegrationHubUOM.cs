﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;

namespace CorzaVeevaIntegration.CommonFunctions
{
    public class FuncCorzaIntegrationHubUOM
    {
        public string GetUOM(string key)
        {
            string json = Environment.GetEnvironmentVariable("UnitMapping");

            var dict = JsonSerializer.Deserialize<Dictionary<string, string>>(json);

            if (dict != null && dict.TryGetValue(key, out string value))
            {
                return value;
            }

            return null;
        }
    }
}
