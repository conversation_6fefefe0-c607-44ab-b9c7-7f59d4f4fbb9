﻿<?xml version="1.0" encoding="utf-8"?>
<!-- https://go.microsoft.com/fwlink/?LinkID=208121. -->
<Project>
  <PropertyGroup>
    <WebPublishMethod>MSDeploy</WebPublishMethod>
    <PublishProvider>AzureWebSite</PublishProvider>
    <LastUsedBuildConfiguration>Release</LastUsedBuildConfiguration>
    <LastUsedPlatform>Any CPU</LastUsedPlatform>
    <SiteUrlToLaunchAfterPublish>https://func-corza-integration-hub.azurewebsites.net</SiteUrlToLaunchAfterPublish>
    <LaunchSiteAfterPublish>false</LaunchSiteAfterPublish>
    <ResourceId>/subscriptions/15d354f0-f25a-4f7b-a126-8d298388edb5/resourceGroups/corza-integration-hub-dev-rg/providers/Microsoft.Web/sites/func-corza-integration-hub</ResourceId>
    <UserName>REDACTED</UserName>
    <_SavePWD>true</_SavePWD>
    <ExcludeApp_Data>false</ExcludeApp_Data>
    <MSDeployServiceURL>func-corza-integration-hub.scm.azurewebsites.net:443</MSDeployServiceURL>
    <MSDeployPublishMethod>WMSVC</MSDeployPublishMethod>
    <SkipExtraFilesOnServer>false</SkipExtraFilesOnServer>
    <EnableMsDeployAppOffline>true</EnableMsDeployAppOffline>
    <EnableMSDeployBackup>true</EnableMSDeployBackup>
    <DeployIisAppPath>func-corza-integration-hub</DeployIisAppPath>
  </PropertyGroup>
</Project>