param(
    [Parameter(Mandatory=$true)]
    [string]$Environment
)

# Set error action preference
$ErrorActionPreference = "Continue"

# Initialize test results array
$testResults = @()

# Set resource group name based on environment
$resourceGroupName = switch ($Environment) {
    "dev" { "corza-integration-hub-devt-rg" }
    "validation" { "corza-integration-hub-val-rg" }
    "prod" { "corza-integration-hub-prod-rg" }
    default { throw "Invalid environment specified" }
}

function Test-ResourceGroup {
    param(
        [string]$resourceGroupName
    )
    
    try {
        $rg = Get-AzResourceGroup -Name $resourceGroupName -ErrorAction Stop
        if ($rg) {
            Write-Host "✅ Resource Group found: $resourceGroupName"
            return $true
        }
    }
    catch {
        Write-Host "❌ Resource Group not found: $resourceGroupName"
        Write-Host "Error: $_"
        return $false
    }
}

function Test-LogAnalytics {
    param(
        [string]$resourceGroupName,
        [string]$environment
    )
    
    $workspaceName = switch ($environment) {
        "dev" { "crz-integration-hub-devt-app-ins-ws" }
        "validation" { "crz-integration-hub-val-app-ins-ws-2025" }
        "prod" { "crz-integration-hub-prod-app-ins-ws" }
    }
    
    try {
        $workspace = Get-AzOperationalInsightsWorkspace -ResourceGroupName $resourceGroupName -Name $workspaceName -ErrorAction Stop
        if ($workspace) {
            $state = $workspace.ProvisioningState
            Write-Host "Log Analytics Workspace '$workspaceName' state: $state"
            
            if ($state -eq "Succeeded") {
                Write-Host "✅ Log Analytics Workspace deployed successfully: $workspaceName"
                return $true
            } else {
                Write-Host "❌ Log Analytics Workspace deployment not successful. Current state: $state"
                return $false
            }
        }
    }
    catch {
        Write-Host "❌ Log Analytics Workspace not found: $workspaceName"
        Write-Host "Error: $_"
        return $false
    }
}

function Test-AppInsights {
    param(
        [string]$resourceGroupName,
        [string]$environment
    )
    
    $appInsightsName = switch ($environment) {
        "dev" { "crz-integration-hub-devt-app-ins" }
        "validation" { "crz-integration-hub-val-app-ins-2025" }
        "prod" { "crz-integration-hub-prod-app-ins" }
    }
    
    try {
        $appInsights = Get-AzApplicationInsights -ResourceGroupName $resourceGroupName -Name $appInsightsName -ErrorAction Stop
        if ($appInsights) {
            $state = $appInsights.ProvisioningState
            Write-Host "Application Insights '$appInsightsName' state: $state"
            
            if ($state -eq "Succeeded") {
                Write-Host "✅ Application Insights deployed successfully: $appInsightsName"
                return $true
            } else {
                Write-Host "❌ Application Insights deployment not successful. Current state: $state"
                return $false
            }
        }
    }
    catch {
        Write-Host "❌ Application Insights not found: $appInsightsName"
        Write-Host "Error: $_"
        return $false
    }
}

function Test-ServiceBus {
    param(
        [string]$resourceGroupName,
        [string]$environment
    )
    
    $serviceBusName = switch ($environment) {
        "dev" { "crz-integration-hub-devt-sbus" }
        "validation" { "corza-integration-hub-val-sbus-2025" }
        "prod" { "corza-integration-hub-prod-sbus" }
    }
    
    try {
        $serviceBus = Get-AzServiceBusNamespace -ResourceGroupName $resourceGroupName -Name $serviceBusName -ErrorAction Stop
        if ($serviceBus) {
            $state = $serviceBus.ProvisioningState
            Write-Host "Service Bus '$serviceBusName' state: $state"
            
            if ($state -eq "Succeeded") {
                Write-Host "✅ Service Bus deployed successfully: $serviceBusName"
                return $true
            } else {
                Write-Host "❌ Service Bus deployment not successful. Current state: $state"
                return $false
            }
        }
    }
    catch {
        Write-Host "❌ Service Bus not found: $serviceBusName"
        Write-Host "Error: $_"
        return $false
    }
}

function Test-KeyVault {
    param(
        [string]$resourceGroupName,
        [string]$environment
    )
    
    $keyVaultName = switch ($environment) {
        "dev" { "crzIntegrationHubDevtkv" }
        "validation" { "corza-val-kv-2027" }
        "prod" { "corza-hub-prd-kv" }
    }
    
    try {
        $keyVault = Get-AzKeyVault -VaultName $keyVaultName -ResourceGroupName $resourceGroupName -ErrorAction Stop
        
        if ($keyVault) {
            Write-Host "✅ Step 1: Key Vault exists with correct name: $keyVaultName"
            
            # Step 2: Verify SKU using Get-AzResource to get detailed properties
            $keyVaultResource = Get-AzResource -ResourceId $keyVault.ResourceId
            $actualSku = $keyVaultResource.Properties.sku.name
            if ($actualSku -eq "standard") {
                Write-Host "✅ Step 2: SKU verification passed. Current SKU: $actualSku"
            } else {
                Write-Host "❌ Step 2: SKU verification failed. Expected: standard, Got: $actualSku"
                return $false
            }

            # Step 3: Verify the vault is active and accessible
            if ($keyVault.VaultUri) {
                Write-Host "✅ Step 3: Key Vault is active and properly provisioned"
                return $true
            } else {
                Write-Host "❌ Step 3: Key Vault exists but may not be properly provisioned"
                return $false
            }
        }
    }
    catch {
        Write-Host "❌ Key Vault verification failed: $($_.Exception.Message)"
        return $false
    }
}

function Test-StorageAccount {
    param(
        [string]$resourceGroupName,
        [string]$environment
    )
    
    $storageName = switch ($environment) {
        "dev" { "crzintegrationhubdevtsa" }
        "validation" { "corzahubval01" }
        "prod" { "corzahubprod01" }
    }
    
    try {
        $storage = Get-AzStorageAccount -ResourceGroupName $resourceGroupName -Name $storageName -ErrorAction Stop
        if ($storage) {
            $state = $storage.ProvisioningState
            Write-Host "Storage Account '$storageName' state: $state"
            
            if ($state -eq "Succeeded") {
                Write-Host "✅ Storage Account deployed successfully: $storageName"
                return $true
            } else {
                Write-Host "❌ Storage Account deployment not successful. Current state: $state"
                return $false
            }
        }
    }
    catch {
        Write-Host "❌ Storage Account not found: $storageName"
        Write-Host "Error: $_"
        return $false
    }
}
function Test-FunctionApp {
    param(
        [string]$resourceGroupName,
        [string]$environment
    )
    
    $functionAppName = switch ($environment) {
        "dev" { "func-corza-integration-hub-devt-2025" }
        "validation" { "func-corza-integration-hub-val" }
        "prod" { "func-corza-integration-hub-prod" }
    }   

    try {
        $functionApp = Get-AzFunctionApp -Name $functionAppName -ResourceGroupName $resourceGroupName -ErrorAction Stop
        
        if ($functionApp) {
            # Step 1: Verify Function App Existence
            Write-Host "✅ Step 1: Function App exists with correct name: $functionAppName"
            
            # Step 2: Verify Runtime Version
            $appSettings = Get-AzFunctionAppSetting -Name $functionAppName -ResourceGroupName $resourceGroupName
            $runtimeVersion = $appSettings["FUNCTIONS_EXTENSION_VERSION"]
            if ($runtimeVersion -eq "~4") {
                Write-Host "✅ Step 2: Runtime version verification passed. Current version: $runtimeVersion"
            }
            else {
                Write-Host "❌ Step 2: Runtime version verification failed. Expected: ~4, Got: $runtimeVersion"
                return $false
            }

            # Step 3: Verify .NET Version
            $dotnetVersion = $functionApp.SiteConfig.NetFrameworkVersion
            if ($dotnetVersion -eq "v8.0") {
                Write-Host "✅ Step 3: .NET version verification passed. Current version: $dotnetVersion"
            }
            else {
                Write-Host "❌ Step 3: .NET version verification failed. Expected: v8.0, Got: $dotnetVersion"
                return $false
            }

            # Step 4: Verify Runtime Type
            $workerRuntime = $appSettings["FUNCTIONS_WORKER_RUNTIME"]
            if ($workerRuntime -eq "dotnet-isolated") {
                Write-Host "✅ Step 4: Runtime type verification passed. Current type: $workerRuntime"
            }
            else {
                Write-Host "❌ Step 4: Runtime type verification failed. Expected: dotnet-isolated, Got: $workerRuntime"
                return $false
            }

            # Step 5: Verify HTTPS Only
            if ($functionApp.HttpsOnly) {
                Write-Host "✅ Step 5: HTTPS Only setting is enabled"
            }
            else {
                Write-Host "❌ Step 5: HTTPS Only setting is disabled"
                return $false
            }

            # Step 6: Verify System-Assigned Identity
            if ($functionApp.IdentityType -eq "SystemAssigned") {
                Write-Host "✅ Step 6: System-assigned identity is enabled"
            }
            else {
                Write-Host "❌ Step 6: System-assigned identity is not enabled"
                return $false
            }

            return $true
        }
    }
    catch {
        Write-Host "❌ Function App verification failed: $($_.Exception.Message)"
        return $false
    }
}	
	
# First check Resource Group
Write-Host "`n🔍 Checking Resource Group..."
$rgExists = Test-ResourceGroup -resourceGroupName $resourceGroupName
$testResults += @{
    Name = "Resource Group"
    Result = $rgExists
}

# Only proceed with resource checks if Resource Group exists
if ($rgExists) {
    Write-Host "`n🔍 Checking Resources..."
    
    # Test Storage Account
    $testResults += @{
        Name = "Storage Account"
        Result = Test-StorageAccount -resourceGroupName $resourceGroupName -environment $Environment
    }

    # Test Key Vault
    $testResults += @{
        Name = "Key Vault"
        Result = Test-KeyVault -resourceGroupName $resourceGroupName -environment $Environment
    }

    # Test Log Analytics
    $testResults += @{
        Name = "Log Analytics Workspace"
        Result = Test-LogAnalytics -resourceGroupName $resourceGroupName -environment $Environment
    }

    # Test Application Insights
    $testResults += @{
        Name = "Application Insights"
        Result = Test-AppInsights -resourceGroupName $resourceGroupName -environment $Environment
    }

    # Test Service Bus
    $testResults += @{
        Name = "Service Bus"
        Result = Test-ServiceBus -resourceGroupName $resourceGroupName -environment $Environment
    }

    # Test Function App
    Write-Host "`n🔍 Checking Function App..."
    $testResults += @{
        Name = "Function App"
        Result = Test-FunctionApp -resourceGroupName $resourceGroupName -environment $Environment
    }
}

# Generate test results summary
$totalTests = $testResults.Count
$passedTests = ($testResults | Where-Object { $_.Result -eq $true }).Count
$failedTests = $totalTests - $passedTests

Write-Host "`n📊 Test Summary:"
Write-Host "Total Tests: $totalTests"
Write-Host "Passed: $passedTests"
Write-Host "Failed: $failedTests"

# Generate JUnit format test results
$jUnitXml = @"
<?xml version="1.0" encoding="UTF-8"?>
<testsuites>
    <testsuite name="Resource Verification Tests" tests="$totalTests" failures="$failedTests">
"@

foreach ($test in $testResults) {
    $status = if ($test.Result) { "passed" } else { "failed" }
    $jUnitXml += @"
        <testcase name="$($test.Name)" status="$status">
"@
    if (-not $test.Result) {
        $jUnitXml += @"
            <failure message="Resource not found or not properly configured"/>
"@
    }
    $jUnitXml += @"
        </testcase>
"@
}

$jUnitXml += @"
    </testsuite>
</testsuites>
"@

# Save test results
$jUnitXml | Out-File "test-results.xml"

# Exit with appropriate code
if ($failedTests -gt 0) {
    exit 1
}
exit 0