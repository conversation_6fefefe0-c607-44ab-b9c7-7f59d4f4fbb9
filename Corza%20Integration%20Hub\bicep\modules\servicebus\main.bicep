param name string
param location string
// param queues array
param topics array

resource sbNamespace 'Microsoft.ServiceBus/namespaces@2024-01-01' = {
  name: name
  location: location
  sku: {
    name: 'Standard'
    tier: 'Standard'
  }
  properties: {}
}

// resource queueResources 'Microsoft.ServiceBus/namespaces/queues@2022-10-01-preview' = [for queueName in queues: {
//   name: queueName
//   parent: sbNamespace
//   properties: {
//     enablePartitioning: true
//   }
// }]

resource topicResources 'Microsoft.ServiceBus/namespaces/topics@2022-10-01-preview' = [for topicName in topics: {
  name: topicName
  parent: sbNamespace
  properties: {
    enablePartitioning: true
    defaultMessageTimeToLive: 'P14D'
  }
}]

output serviceBusId string = sbNamespace.id
