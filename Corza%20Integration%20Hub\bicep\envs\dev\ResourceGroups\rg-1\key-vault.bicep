param keyVaultName string = 'dev-keyvault-2025'
param location string = resourceGroup().location
param skuName string = 'standard'
param tenantId string = subscription().tenantId
param serviceBusNamespace string = 'crz-integration-hub-devt-sbus'
param storageAccountName string = 'crzintegrationhubdevtsa'
param appInsightsName string = 'crz-integration-hub-devt-app-ins'

// Add Application Insights reference
resource appInsights 'Microsoft.Insights/components@2020-02-02' existing = {
  name: appInsightsName
}
// Add storage account reference
resource storageAccount 'Microsoft.Storage/storageAccounts@2023-01-01' existing = {
  name: storageAccountName
}


// Define admin users
param adminUsers array = [
  {
    name: 'SuperAdmins'
    objectId: '87ddee15-24fc-429c-9c41-9e05c3d143f5'
  }
  {
    name: 'Meet'
    objectId: '72468d0e-d449-4ea6-a0d5-1e02a0aa3043'
  }
  {
    name: 'Yash'
    objectId: '92bd6318-a01d-4601-a743-797a93f9ee79'
  }
]

// Convert admin users to access policies
var adminAccessPolicies = [for user in adminUsers: {
  objectId: user.objectId
  tenantId: tenantId
  permissions: {
    secrets: [
      'get'
      'list'
      'set'
      'delete'
    ]
    certificates: [
      'get'
      'list'
      'create'
      'delete'
    ]
    keys: [
      'get'
      'list'
      'create'
      'delete'
    ]
  }
}]

module kvModule '../../../../modules/key_vault/main.bicep' = {
  name: 'kvDeployment'
  scope: resourceGroup()
  params: {
    keyVaultName: keyVaultName
    location: location
    skuName: skuName
    tenantId: tenantId
    accessPolicies: adminAccessPolicies
  }
}

// Add secrets directly to Key Vault
resource azureClientIdSecret 'Microsoft.KeyVault/vaults/secrets@2023-07-01' = {
  name: '${keyVaultName}/crz-integration-hub-app-id'
  properties: {
    value: '0b3e2759-cf9b-4455-a869-9d03d60bd21f'
  }
  dependsOn: [
    kvModule
  ]
}

resource azureClientSecretSecret 'Microsoft.KeyVault/vaults/secrets@2023-07-01' = {
  name: '${keyVaultName}/crz-integration-hub-secret-id'
  properties: {
    value: '****************************************'
  }
  dependsOn: [
    kvModule
  ]
}

resource AzureTenantIdSecret 'Microsoft.KeyVault/vaults/secrets@2023-07-01' = {
  name: '${keyVaultName}/AzureTenantId'
  properties: {
    value: '6106a676-9d32-4a39-ad92-84ad1138266f'
  }
  dependsOn: [
    kvModule
  ]
}

// resource AzureWebJobsStorageSecret 'Microsoft.KeyVault/vaults/secrets@2023-07-01' = {
//   name: '${keyVaultName}/cihub-storage-account-connection-string'
//   properties: {
//     value: 'DefaultEndpointsProtocol=https;AccountName=corzaintegrationhubdevsa;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net'
//   }
//   dependsOn: [
//     kvModule
//   ]
// }

// Add the storage account connection string secret
resource AzureWebJobsStorageSecret 'Microsoft.KeyVault/vaults/secrets@2023-07-01' = {
  name: '${keyVaultName}/cihub-storage-account-connection-string'
  properties: {
    value: 'DefaultEndpointsProtocol=https;AccountName=${storageAccountName};AccountKey=${storageAccount.listKeys().keys[0].value};EndpointSuffix=${environment().suffixes.storage}'
  }
  dependsOn: [
    kvModule
  ]
}

// resource BatchTopicConnectionStringSecret 'Microsoft.KeyVault/vaults/secrets@2023-07-01' = {
//   name: '${keyVaultName}/cihub-sbus-topic-batchchanged-erps-connection-string'
//   properties: {
//     value: 'Endpoint=sb://corza-integration-hub-dev-sbus.servicebus.windows.net/;SharedAccessKeyName=t-batch-dev-SAS;SharedAccessKey=UHqpeLOw5YRZ3Z1Z3yP54wGwQw//GxtAx+ASbHIz7ok='
//   }
//   dependsOn: [
//     kvModule
//   ]
// }

// resource ItemsTopicConnectionStringSecret 'Microsoft.KeyVault/vaults/secrets@2023-07-01' = {
//   name: '${keyVaultName}/cihub-sbus-topic-itemchanged-erps-connection-string'
//   properties: {
//     value: 'Endpoint=sb://corza-integration-hub-dev-sbus.servicebus.windows.net/;SharedAccessKeyName=t-items-dev-SAS;SharedAccessKey=3BTbyAt+uxUng7hY3z9kwzRdjvPZyknL5+ASbLt+f5I='
//   }
//   dependsOn: [
//     kvModule
//   ]
// }

resource BatchTopicConnectionStringSecret 'Microsoft.KeyVault/vaults/secrets@2023-07-01' = {
  name: '${keyVaultName}/cihub-sbus-topic-batchchanged-erps-connection-string'
  properties: {
    value: replace(listKeys('${resourceGroup().id}/providers/Microsoft.ServiceBus/namespaces/${serviceBusNamespace}/topics/topic_batchchanged_erps/authorizationRules/topic_batchchanged_erps-SAS', '2022-10-01-preview').primaryConnectionString, ';EntityPath=topic_batchchanged_erps', '')
  }
  dependsOn: [
    kvModule
  ]
}

resource ItemsTopicConnectionStringSecret 'Microsoft.KeyVault/vaults/secrets@2023-07-01' = {
  name: '${keyVaultName}/cihub-sbus-topic-itemchanged-erps-connection-string'
  properties: {
    value: replace(listKeys('${resourceGroup().id}/providers/Microsoft.ServiceBus/namespaces/${serviceBusNamespace}/topics/topic_itemchanged_erps/authorizationRules/topic_itemchanged_erps-SAS', '2022-10-01-preview').primaryConnectionString, ';EntityPath=topic_itemchanged_erps', '')
  }
  dependsOn: [
    kvModule
  ]
}

resource PasswordSecret 'Microsoft.KeyVault/vaults/secrets@2023-07-01' = {
  name: '${keyVaultName}/veeva-password-secret'
  properties: {
    value: 'F7oK41ILaofYXap'
  }
  dependsOn: [
    kvModule
  ]
}

// Add the Application Insights connection string secret
resource ApplicationInsightsConnectionStringSecret 'Microsoft.KeyVault/vaults/secrets@2023-07-01' = {
  name: '${keyVaultName}/applicationinsights-connection-string'
  properties: {
    value: appInsights.properties.ConnectionString
  }
  dependsOn: [
    kvModule
  ]
}

// Add the Application Insights instrumentation key secret
resource ApplicationInsightsInstrumentationKeySecret 'Microsoft.KeyVault/vaults/secrets@2023-07-01' = {
  name: '${keyVaultName}/applicationinsights-instrumentation-key'
  properties: {
    value: appInsights.properties.InstrumentationKey
  }
  dependsOn: [
    kvModule
  ]
}
