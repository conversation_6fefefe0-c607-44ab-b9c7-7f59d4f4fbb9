﻿<?xml version="1.0" encoding="utf-8"?>
<!-- https://go.microsoft.com/fwlink/?LinkID=208121. -->
<Project>
  <PropertyGroup>
    <WebPublishMethod>MSDeploy</WebPublishMethod>
    <PublishProvider>AzureWebSite</PublishProvider>
    <LastUsedBuildConfiguration>Release</LastUsedBuildConfiguration>
    <LastUsedPlatform>Any CPU</LastUsedPlatform>
    <SiteUrlToLaunchAfterPublish>https://func-corza-integration-hub-devt.azurewebsites.net</SiteUrlToLaunchAfterPublish>
    <LaunchSiteAfterPublish>false</LaunchSiteAfterPublish>
    <UserName>$func-corza-integration-hub-devt</UserName>
    <_SavePWD>true</_SavePWD>
    <ExcludeApp_Data>false</ExcludeApp_Data>
    <MSDeployServiceURL>func-corza-integration-hub-devt.scm.azurewebsites.net:443</MSDeployServiceURL>
    <MSDeployPublishMethod>WMSVC</MSDeployPublishMethod>
    <SkipExtraFilesOnServer>false</SkipExtraFilesOnServer>
    <EnableMsDeployAppOffline>true</EnableMsDeployAppOffline>
    <EnableMSDeployBackup>true</EnableMSDeployBackup>
    <DeployIisAppPath>func-corza-integration-hub-devt</DeployIisAppPath>
  </PropertyGroup>
</Project>