﻿using System;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System.Collections.Generic;

namespace CorzaVeevaIntegration.CommonFunctions
{
    public class FuncCorzaIntegrationHubVeevaAPI
    {
        private readonly IHttpClientFactory _httpClientFactory;
        private readonly ILogger<FuncCorzaIntegrationHubVeevaAPI> _logger;

        // Constructor with dependency injection for HttpClientFactory and Logger
        public FuncCorzaIntegrationHubVeevaAPI(IHttpClientFactory httpClientFactory, ILogger<FuncCorzaIntegrationHubVeevaAPI> logger)
        {
            _httpClientFactory = httpClientFactory ?? throw new ArgumentNullException(nameof(httpClientFactory));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        // Method to call Veeva API asynchronously
        public async Task<ApiResponse> CallVeevaAPIAsync(string apiUrl, string token, string jsonData)
        {
            _logger.LogInformation("Starting API call to Veeva Vault.");

            // Validate API URL
            if (string.IsNullOrEmpty(apiUrl))
            {
                string errorMessage = "API URL is not provided.";
                _logger.LogError(errorMessage);
                return new ApiResponse { IsSuccess = false, ErrorMessage = errorMessage };
            }

            using (var httpClient = _httpClientFactory.CreateClient())
            {
                // Prepare HTTP request
                var requestMessage = new HttpRequestMessage(HttpMethod.Post, apiUrl)
                {
                    Headers =
                    {
                        { "User-Agent", "TBD" },
                        { "Accept", "application/json" },
                        { "Authorization", token }
                    },
                    Content = new StringContent(jsonData, Encoding.UTF8, "application/json")
                };

                HttpResponseMessage response;
                try
                {
                    // Send request
                    response = await httpClient.SendAsync(requestMessage);
                }
                catch (Exception ex)
                {
                    _logger.LogError($"HTTP request failed: {ex.Message}");
                    return new ApiResponse { IsSuccess = false, ErrorMessage = "Failed to send request to Veeva API." };
                }

                string responseContent = await response.Content.ReadAsStringAsync();

                // Handle non-success status codes
                if (!response.IsSuccessStatusCode)
                {
                    _logger.LogError($"Veeva API Error: {response.StatusCode}, Response: {responseContent}");
                    return new ApiResponse { IsSuccess = false, ErrorMessage = responseContent };
                }

                var jsonResponse = JsonConvert.DeserializeObject<VeevaApiResponse>(responseContent);

                // Handle warnings in the response
                if (jsonResponse?.Warnings != null)
                {
                    foreach (var warning in jsonResponse.Warnings)
                    {
                        if (warning.WarningType == "NO_DATA_CHANGES")
                        {
                            _logger.LogWarning($"Veeva API Warning Ignored: {warning.WarningType} - {warning.Message}");
                            continue;
                        }
                        return new ApiResponse { IsSuccess = false, ErrorMessage = $"Warning: {warning.WarningType} - {warning.Message}" };
                    }
                }

                // Handle errors in the response
                if (jsonResponse?.Errors != null)
                {
                    foreach (var error in jsonResponse.Errors)
                    {
                        return new ApiResponse { IsSuccess = false, ErrorMessage = $"Error: {error.Type} - {error.Message}" };
                    }
                }

                // Process data objects returned in the response
                if (jsonResponse?.Data != null)
                {
                    foreach (var dataItem in jsonResponse.Data)
                    {
                        // Check for errors in data items
                        if (dataItem.Errors != null)
                        {
                            foreach (var error in dataItem.Errors)
                            {
                                return new ApiResponse { IsSuccess = false, ErrorMessage = $"Error: {error.Type} - {error.Message}" };
                            }
                        }

                        // Check for warnings in data items
                        if (dataItem.Warnings != null)
                        {
                            foreach (var warning in dataItem.Warnings)
                            {
                                if (warning.WarningType == "NO_DATA_CHANGES")
                                {
                                    _logger.LogWarning($"Veeva API Warning Ignored: {warning.WarningType} - {warning.Message}");
                                    continue;
                                }
                                return new ApiResponse { IsSuccess = false, ErrorMessage = $"Warning: {warning.WarningType} - {warning.Message}" };
                            }
                        }
                    }
                }

                _logger.LogInformation("Successfully executed the Veeva API call.");
                return new ApiResponse { IsSuccess = true, Data = responseContent };
            }
        }
    }

    // Model class for API response
    public class ApiResponse
    {
        public bool IsSuccess { get; set; }
        public string Data { get; set; }
        public string ErrorMessage { get; set; }
    }

    // Model class for Veeva API response
    public class VeevaApiResponse
    {
        [JsonProperty("warnings")]
        public List<VeevaApiWarning> Warnings { get; set; }

        [JsonProperty("errors")]
        public List<VeevaApiError> Errors { get; set; }

        [JsonProperty("data")]
        public List<VeevaApiData> Data { get; set; }
    }

    // Model class for API warnings
    public class VeevaApiWarning
    {
        [JsonProperty("warning_type")]
        public string WarningType { get; set; }

        [JsonProperty("message")]
        public string Message { get; set; }
    }

    // Model class for API errors
    public class VeevaApiError
    {
        [JsonProperty("type")]
        public string Type { get; set; }

        [JsonProperty("message")]
        public string Message { get; set; }
    }

    // Model class for API data response
    public class VeevaApiData
    {
        [JsonProperty("warnings")]
        public List<VeevaApiWarning> Warnings { get; set; }

        [JsonProperty("errors")]
        public List<VeevaApiError> Errors { get; set; }
    }
}
