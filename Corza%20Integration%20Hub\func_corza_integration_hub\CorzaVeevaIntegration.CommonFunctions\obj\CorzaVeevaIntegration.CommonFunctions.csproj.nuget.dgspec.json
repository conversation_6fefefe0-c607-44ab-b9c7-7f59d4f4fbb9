{"format": 1, "restore": {"C:\\Corza\\Repos\\func_corza_integration_hub\\CorzaVeevaIntegration.CommonFunctions\\CorzaVeevaIntegration.CommonFunctions.csproj": {}}, "projects": {"C:\\Corza\\Repos\\func_corza_integration_hub\\CorzaVeevaIntegration.CommonFunctions\\CorzaVeevaIntegration.CommonFunctions.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Corza\\Repos\\func_corza_integration_hub\\CorzaVeevaIntegration.CommonFunctions\\CorzaVeevaIntegration.CommonFunctions.csproj", "projectName": "CorzaVeevaIntegration.CommonFunctions", "projectPath": "C:\\Corza\\Repos\\func_corza_integration_hub\\CorzaVeevaIntegration.CommonFunctions\\CorzaVeevaIntegration.CommonFunctions.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Corza\\Repos\\func_corza_integration_hub\\CorzaVeevaIntegration.CommonFunctions\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Azure.Messaging.ServiceBus": {"target": "Package", "version": "[7.19.0, )"}, "Microsoft.Azure.Functions.Worker.Extensions.ServiceBus": {"target": "Package", "version": "[5.22.2, )"}, "Microsoft.Extensions.Http": {"target": "Package", "version": "[9.0.4, )"}, "Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[9.0.4, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[8.0.15, 8.0.15]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[8.0.15, 8.0.15]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[8.0.15, 8.0.15]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203/PortableRuntimeIdentifierGraph.json"}}}}}