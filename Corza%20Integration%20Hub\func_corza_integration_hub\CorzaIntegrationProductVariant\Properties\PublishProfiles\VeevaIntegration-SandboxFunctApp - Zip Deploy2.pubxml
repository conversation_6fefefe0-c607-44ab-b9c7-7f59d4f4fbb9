﻿<?xml version="1.0" encoding="utf-8"?>
<!-- https://go.microsoft.com/fwlink/?LinkID=208121. -->
<Project>
  <PropertyGroup>
    <WebPublishMethod>ZipDeploy</WebPublishMethod>
    <PublishProvider>AzureWebSite</PublishProvider>
    <LastUsedBuildConfiguration>Release</LastUsedBuildConfiguration>
    <LastUsedPlatform>Any CPU</LastUsedPlatform>
    <SiteUrlToLaunchAfterPublish>https://veevaintegration-sandboxfunctapp.azurewebsites.net</SiteUrlToLaunchAfterPublish>
    <LaunchSiteAfterPublish>false</LaunchSiteAfterPublish>
    <UserName>$VeevaIntegration-SandboxFunctApp</UserName>
    <_SavePWD>true</_SavePWD>
    <PublishUrl>https://veevaintegration-sandboxfunctapp.scm.azurewebsites.net/</PublishUrl>
  </PropertyGroup>
</Project>